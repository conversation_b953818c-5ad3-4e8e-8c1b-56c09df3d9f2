import React, { useState } from 'react';
import { Button, Space, Card, Input, message, Descriptions, Typography, Row, Col } from 'antd';
import {
  ApiOutlined,
  FileTextOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  InfoCircleOutlined,
  BugOutlined,
  WindowsOutlined,
} from '@ant-design/icons';

import { useElectronAPI, useDialogs, useFileOperations } from '../hooks/useElectronAPI';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

export const IPCTestPanel: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [fileContent, setFileContent] = useState('');
  const [filePath, setFilePath] = useState('');
  const electronAPI = useElectronAPI();
  const dialogs = useDialogs();
  const fileOps = useFileOperations();

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testAppInfo = async () => {
    try {
      if (!electronAPI) {
        throw new Error('Electron API not available');
      }
      const info = await electronAPI.app.getInfo();
      addTestResult(`应用信息获取成功: ${info.name} v${info.version}`);
      message.success('应用信息获取成功');
    } catch (error) {
      addTestResult(`应用信息获取失败: ${error}`);
      message.error('应用信息获取失败');
    }
  };

  const testWindowControls = async () => {
    try {
      if (!electronAPI) {
        throw new Error('Electron API not available');
      }
      const state = await electronAPI.window.getState();
      addTestResult(`窗口状态: 最大化=${state.isMaximized}, 最小化=${state.isMinimized}`);
      message.success('窗口状态获取成功');
    } catch (error) {
      addTestResult(`窗口状态获取失败: ${error}`);
      message.error('窗口状态获取失败');
    }
  };

  const testDialog = async () => {
    try {
      const result = await dialogs.showMessage({
        type: 'info',
        title: 'IPC 测试',
        message: '这是一个 IPC 通信测试对话框',
        buttons: ['确定', '取消'],
      });
      addTestResult(`对话框测试: 用户点击了 ${result.response === 0 ? '确定' : '取消'}`);
      message.success('对话框测试成功');
    } catch (error) {
      addTestResult(`对话框测试失败: ${error}`);
      message.error('对话框测试失败');
    }
  };

  const testFileDialog = async () => {
    try {
      const result = await dialogs.showOpenDialog({
        title: '选择文件',
        filters: [
          { name: '文本文件', extensions: ['txt', 'md'] },
          { name: '所有文件', extensions: ['*'] },
        ],
        properties: ['openFile'],
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const selectedPath = result.filePaths[0];
        setFilePath(selectedPath);
        addTestResult(`文件选择成功: ${selectedPath}`);
        message.success('文件选择成功');
      } else {
        addTestResult('文件选择被取消');
      }
    } catch (error) {
      addTestResult(`文件选择失败: ${error}`);
      message.error('文件选择失败');
    }
  };

  const testFileRead = async () => {
    if (!filePath) {
      message.warning('请先选择一个文件');
      return;
    }

    try {
      const content = await fileOps.readFile(filePath);
      setFileContent(content);
      addTestResult(`文件读取成功: ${filePath} (${content.length} 字符)`);
      message.success('文件读取成功');
    } catch (error) {
      addTestResult(`文件读取失败: ${error}`);
      message.error('文件读取失败');
    }
  };

  const testFileWrite = async () => {
    if (!fileContent) {
      message.warning('请输入要写入的内容');
      return;
    }

    try {
      const result = await dialogs.showSaveDialog({
        title: '保存文件',
        defaultPath: 'test.txt',
        filters: [
          { name: '文本文件', extensions: ['txt'] },
          { name: '所有文件', extensions: ['*'] },
        ],
      });

      if (!result.canceled && result.filePath) {
        await fileOps.writeFile(result.filePath, fileContent);
        addTestResult(`文件写入成功: ${result.filePath}`);
        message.success('文件写入成功');
      }
    } catch (error) {
      addTestResult(`文件写入失败: ${error}`);
      message.error('文件写入失败');
    }
  };

  const testDevTools = async () => {
    try {
      if (!electronAPI) {
        throw new Error('Electron API not available');
      }
      const isOpen = await electronAPI.devTools.isOpen();
      if (isOpen) {
        electronAPI.devTools.close();
        addTestResult('开发者工具已关闭');
      } else {
        electronAPI.devTools.open();
        addTestResult('开发者工具已打开');
      }
      message.success('开发者工具切换成功');
    } catch (error) {
      addTestResult(`开发者工具切换失败: ${error}`);
      message.error('开发者工具切换失败');
    }
  };

  const clearResults = () => {
    setTestResults([]);
    message.info('测试结果已清空');
  };

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="IPC 功能测试" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Space wrap>
                <Button icon={<InfoCircleOutlined />} onClick={testAppInfo}>
                  应用信息
                </Button>
                <Button icon={<WindowsOutlined />} onClick={testWindowControls}>
                  窗口状态
                </Button>
                <Button icon={<ApiOutlined />} onClick={testDialog}>
                  对话框
                </Button>
                <Button icon={<BugOutlined />} onClick={testDevTools}>
                  开发工具
                </Button>
              </Space>

              <Space wrap>
                <Button icon={<FolderOpenOutlined />} onClick={testFileDialog}>
                  选择文件
                </Button>
                <Button icon={<FileTextOutlined />} onClick={testFileRead} disabled={!filePath}>
                  读取文件
                </Button>
                <Button icon={<SaveOutlined />} onClick={testFileWrite} disabled={!fileContent}>
                  保存文件
                </Button>
              </Space>

              {filePath && (
                <div>
                  <Text strong>选中文件: </Text>
                  <Text code>{filePath}</Text>
                </div>
              )}
            </Space>
          </Card>

          <Card title="文件内容编辑" size="small" style={{ marginTop: '16px' }}>
            <TextArea
              rows={6}
              value={fileContent}
              onChange={e => setFileContent(e.target.value)}
              placeholder="在这里输入或编辑文件内容..."
            />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title="测试结果"
            size="small"
            extra={
              <Button size="small" onClick={clearResults}>
                清空
              </Button>
            }
          >
            <div
              style={{
                height: '300px',
                overflowY: 'auto',
                backgroundColor: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px',
                fontFamily: 'monospace',
                fontSize: '12px',
              }}
            >
              {testResults.length === 0 ? (
                <Text type="secondary">暂无测试结果</Text>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    {result}
                  </div>
                ))
              )}
            </div>
          </Card>
        </Col>
      </Row>

      <Card title="使用说明" size="small" style={{ marginTop: '16px' }}>
        <Paragraph>
          <Text strong>IPC 通信测试面板</Text> 用于验证 Electron 主进程和渲染进程之间的通信功能：
        </Paragraph>
        <ul>
          <li><Text strong>应用信息：</Text>获取应用的基本信息（名称、版本、平台等）</li>
          <li><Text strong>窗口状态：</Text>获取当前窗口的状态信息</li>
          <li><Text strong>对话框：</Text>测试系统对话框的显示功能</li>
          <li><Text strong>文件操作：</Text>测试文件的选择、读取和写入功能</li>
          <li><Text strong>开发工具：</Text>切换开发者工具的显示状态</li>
        </ul>
      </Card>
    </div>
  );
};
