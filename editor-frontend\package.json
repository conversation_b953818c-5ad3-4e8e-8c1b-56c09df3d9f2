{"name": "editor-frontend", "private": true, "version": "1.0.0", "description": "桌面端图书编辑器前端应用", "main": "dist/main/main/main.js", "homepage": "./", "type": "module", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:main": "tsc -p tsconfig.main.json", "build:preload": "tsc -p tsconfig.preload.json", "build:all": "npm run build && npm run build:main", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint:check": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "type-check:main": "tsc -p tsconfig.main.json --noEmit", "type-check:all": "npm run type-check && npm run type-check:main", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && npm run build:main && npm run build:preload && electron .\"", "electron:build": "npm run build:all && electron-builder", "electron:build:win": "npm run build:all && electron-builder --win", "electron:build:mac": "npm run build:all && electron-builder --mac", "electron:build:linux": "npm run build:all && electron-builder --linux", "electron:pack": "npm run build:all && electron-builder --dir", "electron:dist": "npm run build:all && electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1", "test:lint": "npm run lint:check", "test:format": "npm run format:check", "test:type": "npm run type-check:all", "test:all": "npm run test:lint && npm run test:format && npm run test:type", "clean": "rimraf dist out node_modules/.cache .eslintcache", "clean:all": "npm run clean && rimraf node_modules", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@grapesjs/react": "^2.0.0", "antd": "^5.27.1", "axios": "^1.11.0", "grapesjs": "^0.22.12", "react": "^19.1.1", "react-dom": "^19.1.1", "react-error-boundary": "^6.0.0", "react-router-dom": "^6.30.1", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/node": "^20.19.11", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vitejs/plugin-react": "^5.0.0", "concurrently": "^8.2.2", "cross-env": "^10.0.0", "electron": "^28.3.3", "electron-builder": "^24.13.3", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "rimraf": "^5.0.10", "sass-embedded": "^1.90.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "wait-on": "^7.2.0"}}