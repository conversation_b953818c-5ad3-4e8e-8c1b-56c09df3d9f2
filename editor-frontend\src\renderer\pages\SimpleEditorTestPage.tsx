/**
 * 简化编辑器测试页面
 */

import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { ErrorFallback } from '../components/ErrorFallback';
import SimpleEditorTest from '../components/Editor/SimpleEditorTest';

const SimpleEditorTestPage: React.FC = () => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('简化编辑器测试页面错误:', error, errorInfo);
      }}
    >
      <SimpleEditorTest />
    </ErrorBoundary>
  );
};

export default SimpleEditorTestPage;
