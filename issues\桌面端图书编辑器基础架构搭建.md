# 桌面端图书编辑器基础架构搭建任务

## 任务概述
创建一个桌面端图书编辑器项目的完整基础架构，使用 monorepo 模式组织代码。

## 技术栈
- **前端**：Electron 28.x + React 18.x + TypeScript 5.x + Vite
- **后端**：Django 4.2.x + Django REST Framework + Python 3.11
- **开发工具**：ESLint + Prettier + pre-commit hooks

## 项目结构
```
htmlEditor/
├── editor-frontend/          # Electron + React 前端
├── editor-backend/           # Django REST API 后端
├── shared/                   # 共享类型定义和工具
├── docs/                     # 项目文档
└── scripts/                  # 构建和部署脚本
```

## 详细执行计划

### 阶段1：项目基础架构搭建（30分钟）
1. 创建完整的 monorepo 目录结构
2. 初始化 Git 仓库和配置
3. 创建根级别的 package.json（workspace 管理）
4. 配置 .gitignore 文件
5. 创建基础 README.md

### 阶段2：前端项目初始化（60分钟）
1. 使用 Vite 创建 React TypeScript 项目
2. 安装和配置 Electron 依赖
3. 配置 Electron 主进程（main.ts）和预加载脚本（preload.ts）
4. 配置 Vite 构建和开发环境
5. 创建基础的 React 组件结构和路由

### 阶段3：后端项目初始化（45分钟）
1. 创建 Python 虚拟环境
2. 安装 Django 和 Django REST Framework
3. 初始化 Django 项目结构
4. 配置数据库和基础设置
5. 创建基础 API 应用结构

### 阶段4：开发工具配置（45分钟）
1. 配置前端 ESLint 和 Prettier
2. 配置 TypeScript 严格模式
3. 设置 pre-commit hooks
4. 创建开发和构建脚本
5. 配置代码格式化规则

### 阶段5：基础功能验证（60分钟）
1. 实现前端基础布局和路由
2. 创建后端基础 API 端点
3. 配置 CORS 支持前后端通信
4. 验证 Electron 应用启动
5. 测试前后端 API 通信

### 阶段6：文档和说明（30分钟）
1. 完善项目 README.md
2. 创建安装和运行指南
3. 编写开发环境设置文档
4. 创建项目结构说明

## 验收标准

### 环境验证
- [ ] 前端：`cd editor-frontend && npm install && npm run dev` 成功启动
- [ ] 后端：`cd editor-backend && pip install -r requirements.txt && python manage.py runserver` 成功启动
- [ ] Electron：`npm run electron:dev` 成功打开桌面应用窗口

### 代码质量
- [ ] 所有 TypeScript 文件通过类型检查
- [ ] ESLint 和 Prettier 检查无错误
- [ ] pre-commit hooks 正常工作

### 功能验证
- [ ] 前后端可以正常通信（测试 API 调用）
- [ ] Electron 应用可以加载 React 页面
- [ ] 开发环境热重载正常工作

## 预计完成时间
总计：4.5小时

## 风险和注意事项
1. 确保 Node.js 和 Python 版本符合要求
2. 注意 Electron 安全配置（禁用 nodeIntegration，启用 contextIsolation）
3. 确保 CORS 配置正确，支持前后端通信
4. 验证所有依赖版本兼容性

## 成功标准
项目搭建完成后，应该能够：
1. 成功启动 Electron 桌面应用
2. 前端 React 应用正常渲染
3. 后端 Django API 正常响应
4. 前后端通信正常
5. 开发环境热重载工作正常
6. 代码质量检查通过
