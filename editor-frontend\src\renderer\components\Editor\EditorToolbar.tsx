/**
 * 编辑器工具栏组件
 * 提供保存、撤销、重做、预览等功能
 */

import {
  ClearOutlined,
  DesktopOutlined,
  DownloadOutlined,
  EyeOutlined,
  MobileOutlined,
  RedoOutlined,
  SaveOutlined,
  SettingOutlined,
  TabletOutlined,
  UndoOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { Button, Divider, Dropdown, Modal, Space, Tooltip, Typography } from 'antd';
import type { Editor } from 'grapesjs';
import React, { useCallback, useState } from 'react';
import type { EditorActions, EditorState } from '../../../hooks/useEditor';

const { Text } = Typography;

/**
 * 工具栏组件属性接口
 */
export interface EditorToolbarProps {
  /** 编辑器实例 */
  editor: Editor | null;
  /** 编辑器状态 */
  state: EditorState;
  /** 编辑器操作 */
  actions: EditorActions;
  /** 保存回调 */
  onSave?: () => void;
}

/**
 * 设备类型
 */
const DEVICES = [
  { id: 'desktop', name: '桌面', icon: <DesktopOutlined /> },
  { id: 'tablet', name: '平板', icon: <TabletOutlined /> },
  { id: 'mobile', name: '手机', icon: <MobileOutlined /> },
];

/**
 * 编辑器工具栏组件
 */
const EditorToolbar: React.FC<EditorToolbarProps> = ({ editor, state, actions, onSave }) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewContent, setPreviewContent] = useState({ html: '', css: '' });
  const [currentDevice, setCurrentDevice] = useState('desktop');

  /**
   * 处理保存
   */
  const handleSave = useCallback(() => {
    onSave?.();
  }, [onSave]);

  /**
   * 处理撤销
   */
  const handleUndo = useCallback(() => {
    actions.undo();
  }, [actions]);

  /**
   * 处理重做
   */
  const handleRedo = useCallback(() => {
    actions.redo();
  }, [actions]);

  /**
   * 处理预览
   */
  const handlePreview = useCallback(() => {
    if (!editor) return;

    const content = actions.exportContent();
    setPreviewContent(content);
    setPreviewVisible(true);
  }, [editor, actions]);

  /**
   * 处理清空
   */
  const handleClear = useCallback(() => {
    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有内容吗？此操作不可撤销。',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        actions.clearContent();
      },
    });
  }, [actions]);

  /**
   * 处理导出
   */
  const handleExport = useCallback(() => {
    if (!editor) return;

    const content = actions.exportContent();
    const blob = new Blob([JSON.stringify(content, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `book-content-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [editor, actions]);

  /**
   * 处理导入
   */
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = event => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = e => {
        try {
          const content = JSON.parse(e.target?.result as string);
          actions.loadContent(content);
        } catch (error) {
          console.error('导入失败:', error);
          Modal.error({
            title: '导入失败',
            content: '文件格式不正确，请选择有效的 JSON 文件。',
          });
        }
      };
      reader.readAsText(file);
    };
    input.click();
  }, [actions]);

  /**
   * 处理设备切换
   */
  const handleDeviceChange = useCallback(
    (deviceId: string) => {
      if (!editor) return;

      const deviceManager = editor.DeviceManager;
      deviceManager.select(deviceId);
      setCurrentDevice(deviceId);
    },
    [editor]
  );

  /**
   * 获取当前设备信息
   */
  const getCurrentDevice = () => {
    return DEVICES.find(device => device.id === currentDevice) || DEVICES[0];
  };

  /**
   * 设备切换菜单
   */
  const deviceMenuItems = DEVICES.map(device => ({
    key: device.id,
    label: (
      <Space>
        {device.icon}
        {device.name}
      </Space>
    ),
    onClick: () => handleDeviceChange(device.id),
  }));

  /**
   * 更多操作菜单
   */
  const moreMenuItems = [
    {
      key: 'export',
      label: (
        <Space>
          <DownloadOutlined />
          导出内容
        </Space>
      ),
      onClick: handleExport,
    },
    {
      key: 'import',
      label: (
        <Space>
          <UploadOutlined />
          导入内容
        </Space>
      ),
      onClick: handleImport,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'clear',
      label: (
        <Space>
          <ClearOutlined />
          清空内容
        </Space>
      ),
      onClick: handleClear,
      danger: true,
    },
  ];

  return (
    <>
      <div className='book-editor-toolbar'>
        <Space split={<Divider type='vertical' />}>
          {/* 文件操作 */}
          <Space>
            <Tooltip title='保存 (Ctrl+S)'>
              <Button
                type='primary'
                icon={<SaveOutlined />}
                onClick={handleSave}
                disabled={!state.hasUnsavedChanges}
              >
                保存
              </Button>
            </Tooltip>
          </Space>

          {/* 编辑操作 */}
          <Space>
            <Tooltip title='撤销 (Ctrl+Z)'>
              <Button icon={<UndoOutlined />} onClick={handleUndo} disabled={!state.canUndo}>
                撤销
              </Button>
            </Tooltip>
            <Tooltip title='重做 (Ctrl+Y)'>
              <Button icon={<RedoOutlined />} onClick={handleRedo} disabled={!state.canRedo}>
                重做
              </Button>
            </Tooltip>
          </Space>

          {/* 预览和设备 */}
          <Space>
            <Tooltip title='预览'>
              <Button icon={<EyeOutlined />} onClick={handlePreview} disabled={!editor}>
                预览
              </Button>
            </Tooltip>
            <Dropdown menu={{ items: deviceMenuItems }} placement='bottomLeft'>
              <Button>
                <Space>
                  {getCurrentDevice().icon}
                  {getCurrentDevice().name}
                </Space>
              </Button>
            </Dropdown>
          </Space>

          {/* 更多操作 */}
          <Space>
            <Dropdown menu={{ items: moreMenuItems }} placement='bottomRight'>
              <Button icon={<SettingOutlined />}>更多</Button>
            </Dropdown>
          </Space>
        </Space>

        {/* 状态信息 */}
        <Space>
          {state.hasUnsavedChanges && <Text type='warning'>未保存</Text>}
          {state.currentFile && <Text type='secondary'>{state.currentFile}</Text>}
        </Space>
      </div>

      {/* 预览模态框 */}
      <Modal
        title='内容预览'
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width='80%'
        style={{ top: 20 }}
        bodyStyle={{ padding: 0, height: '70vh' }}
      >
        <iframe
          srcDoc={`
            <!DOCTYPE html>
            <html>
              <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <title>预览</title>
                <style>
                  body {
                    margin: 0;
                    padding: 20px;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.6;
                    color: #333;
                  }
                  ${previewContent.css}
                </style>
              </head>
              <body>
                ${previewContent.html}
              </body>
            </html>
          `}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
          }}
        />
      </Modal>
    </>
  );
};

export default React.memo(EditorToolbar);
