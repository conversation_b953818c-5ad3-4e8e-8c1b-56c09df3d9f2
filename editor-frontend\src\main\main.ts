import fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

import { app, BrowserWindow, dialog, ipcMain, Menu, shell } from 'electron';

import { getPreloadPath, getUIPath, isDev } from './utils.js';

// ES 模块中获取 __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 保持对窗口对象的全局引用，如果不这样做，当 JavaScript 对象被垃圾回收时，窗口会自动关闭
let mainWindow: BrowserWindow | null = null;

const createWindow = (): void => {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: false,
    icon: join(__dirname, '../../assets/icon.png'),
    webPreferences: {
      preload: getPreloadPath(),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: isDev ? false : true,
      allowRunningInsecureContent: isDev,
      experimentalFeatures: false,
    },
  });

  // 加载应用
  const uiPath = getUIPath();
  if (isDev) {
    mainWindow.loadURL(uiPath);
    // 开发环境下打开开发者工具
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(uiPath);
  }

  // 当窗口准备好显示时显示窗口
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();

      // 开发环境下聚焦窗口
      if (isDev) {
        mainWindow.focus();
      }
    }
  });

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
};

// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  createWindow();
  createMenu();
  registerIpcHandlers();

  app.on('activate', () => {
    // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，通常会重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  // 在 macOS 上，应用和它们的菜单栏通常会保持活跃状态，直到用户使用 Cmd + Q 明确退出
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 创建应用菜单
const createMenu = (): void => {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建图书',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow?.webContents.send('menu:new-book');
          },
        },
        {
          label: '打开图书',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            if (!mainWindow) return;

            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: '图书文件', extensions: ['book', 'json'] },
                { name: '所有文件', extensions: ['*'] },
              ],
            });

            if (!result.canceled && result.filePaths.length > 0) {
              mainWindow.webContents.send('menu:open-book', result.filePaths[0]);
            }
          },
        },
        {
          label: '保存',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow?.webContents.send('menu:save-book');
          },
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          },
        },
      ],
    },
    {
      label: '编辑',
      submenu: [
        {
          label: '撤销',
          accelerator: 'CmdOrCtrl+Z',
          role: 'undo',
        },
        {
          label: '重做',
          accelerator: 'CmdOrCtrl+Y',
          role: 'redo',
        },
        { type: 'separator' },
        {
          label: '剪切',
          accelerator: 'CmdOrCtrl+X',
          role: 'cut',
        },
        {
          label: '复制',
          accelerator: 'CmdOrCtrl+C',
          role: 'copy',
        },
        {
          label: '粘贴',
          accelerator: 'CmdOrCtrl+V',
          role: 'paste',
        },
      ],
    },
    {
      label: '视图',
      submenu: [
        {
          label: '重新加载',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow?.webContents.reload();
          },
        },
        {
          label: '强制重新加载',
          accelerator: 'CmdOrCtrl+Shift+R',
          click: () => {
            mainWindow?.webContents.reloadIgnoringCache();
          },
        },
        {
          label: '开发者工具',
          accelerator: 'F12',
          click: () => {
            mainWindow?.webContents.toggleDevTools();
          },
        },
        { type: 'separator' },
        {
          label: '全屏',
          accelerator: 'F11',
          click: () => {
            if (mainWindow) {
              mainWindow.setFullScreen(!mainWindow.isFullScreen());
            }
          },
        },
      ],
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow!, {
              type: 'info',
              title: '关于',
              message: '桌面端图书编辑器',
              detail: '版本 1.0.0\n基于 Electron、React 和 Django 构建',
            });
          },
        },
      ],
    },
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
};

// 注册 IPC 处理器
const registerIpcHandlers = (): void => {
  // 应用相关处理器
  ipcMain.handle('app:get-version', () => app.getVersion());

  ipcMain.handle('app:get-path', (_, name: string) => app.getPath(name as any));

  ipcMain.handle('app:get-info', () => ({
    name: app.getName(),
    version: app.getVersion(),
    platform: process.platform,
    arch: process.arch,
    electronVersion: process.versions.electron,
    chromeVersion: process.versions.chrome,
    nodeVersion: process.versions.node,
  }));

  ipcMain.handle('app:quit', () => app.quit());

  ipcMain.handle('app:restart', () => app.relaunch({ args: process.argv.slice(1) }));

  ipcMain.handle('app:get-performance-metrics', () => ({
    memoryUsage: process.memoryUsage(),
    cpuUsage: process.cpuUsage(),
    timestamp: Date.now(),
  }));

  // 窗口相关处理器
  ipcMain.handle('window:minimize', () => mainWindow?.minimize());

  ipcMain.handle('window:maximize', () => {
    if (mainWindow?.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow?.maximize();
    }
  });

  ipcMain.handle('window:unmaximize', () => mainWindow?.unmaximize());

  ipcMain.handle('window:close', () => mainWindow?.close());

  ipcMain.handle('window:set-title', (_, title: string) => mainWindow?.setTitle(title));

  ipcMain.handle('window:set-size', (_, width: number, height: number) => {
    mainWindow?.setSize(width, height);
  });

  ipcMain.handle('window:center', () => mainWindow?.center());

  ipcMain.handle('window:get-state', () => {
    if (!mainWindow) return null;
    return {
      isMaximized: mainWindow.isMaximized(),
      isMinimized: mainWindow.isMinimized(),
      isFullscreen: mainWindow.isFullScreen(),
      isFocused: mainWindow.isFocused(),
      bounds: mainWindow.getBounds(),
    };
  });

  // 对话框相关处理器
  ipcMain.handle('dialog:show-message', async (_, options) => {
    if (!mainWindow) return { response: 0 };
    return await dialog.showMessageBox(mainWindow, options);
  });

  ipcMain.handle('dialog:show-error', (_, title: string, content: string) => {
    if (!mainWindow) return;
    dialog.showErrorBox(title, content);
  });

  ipcMain.handle('dialog:show-open', async (_, options) => {
    if (!mainWindow) return { canceled: true };
    return await dialog.showOpenDialog(mainWindow, options);
  });

  ipcMain.handle('dialog:show-save', async (_, options) => {
    if (!mainWindow) return { canceled: true };
    return await dialog.showSaveDialog(mainWindow, options);
  });

  // 文件系统相关处理器
  ipcMain.handle('file:read', async (_, filePath: string) => {
    try {
      return await fs.promises.readFile(filePath, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to read file: ${error}`);
    }
  });

  ipcMain.handle('file:write', async (_, filePath: string, content: string) => {
    try {
      await fs.promises.writeFile(filePath, content, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to write file: ${error}`);
    }
  });

  ipcMain.handle('file:exists', async (_, filePath: string) => {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  });

  ipcMain.handle('file:delete', async (_, filePath: string) => {
    try {
      await fs.promises.unlink(filePath);
    } catch (error) {
      throw new Error(`Failed to delete file: ${error}`);
    }
  });

  ipcMain.handle('file:copy', async (_, src: string, dest: string) => {
    try {
      await fs.promises.copyFile(src, dest);
    } catch (error) {
      throw new Error(`Failed to copy file: ${error}`);
    }
  });

  ipcMain.handle('file:move', async (_, src: string, dest: string) => {
    try {
      await fs.promises.rename(src, dest);
    } catch (error) {
      throw new Error(`Failed to move file: ${error}`);
    }
  });

  // 文件夹相关处理器
  ipcMain.handle('folder:create', async (_, folderPath: string) => {
    try {
      await fs.promises.mkdir(folderPath, { recursive: true });
    } catch (error) {
      throw new Error(`Failed to create folder: ${error}`);
    }
  });

  ipcMain.handle('folder:read', async (_, folderPath: string) => {
    try {
      return await fs.promises.readdir(folderPath);
    } catch (error) {
      throw new Error(`Failed to read folder: ${error}`);
    }
  });

  // Shell 相关处理器
  ipcMain.handle('shell:open-external', async (_, url: string) => {
    await shell.openExternal(url);
  });

  ipcMain.handle('shell:show-item-in-folder', (_, fullPath: string) => {
    shell.showItemInFolder(fullPath);
  });

  ipcMain.handle('shell:open-path', async (_, path: string) => {
    return await shell.openPath(path);
  });

  // 开发工具相关处理器
  ipcMain.handle('dev-tools:is-open', () => {
    return mainWindow?.webContents.isDevToolsOpened() || false;
  });

  ipcMain.handle('dev-tools:open', () => {
    mainWindow?.webContents.openDevTools();
  });

  ipcMain.handle('dev-tools:close', () => {
    mainWindow?.webContents.closeDevTools();
  });

  ipcMain.handle('dev-tools:toggle', () => {
    mainWindow?.webContents.toggleDevTools();
  });
};

// 处理应用协议
app.setAsDefaultProtocolClient('book-editor');

// 防止多个实例
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // 当运行第二个实例时，将会聚焦到 mainWindow 这个窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
