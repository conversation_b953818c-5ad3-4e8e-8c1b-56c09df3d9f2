{"name": "book-editor", "version": "1.0.0", "description": "A desktop book editor application built with Electron, React, and Django", "private": true, "workspaces": ["editor-frontend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd editor-frontend && npm run dev", "dev:backend": "cd editor-backend && python manage.py runserver", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd editor-frontend && npm run build", "build:backend": "cd editor-backend && python manage.py collectstatic --noinput", "electron:dev": "cd editor-frontend && npm run electron:dev", "electron:build": "cd editor-frontend && npm run electron:build", "install:all": "npm install && cd editor-frontend && npm install && cd ../editor-backend && pip install -r requirements.txt", "lint": "npm run lint:frontend", "lint:frontend": "cd editor-frontend && npm run lint", "format": "npm run format:frontend", "format:frontend": "cd editor-frontend && npm run format", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd editor-frontend && npm run test", "test:backend": "cd editor-backend && python manage.py test", "clean": "rimraf node_modules editor-frontend/node_modules editor-frontend/dist editor-frontend/out editor-backend/__pycache__ editor-backend/db.sqlite3"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/book-editor.git"}, "keywords": ["electron", "react", "django", "book-editor", "desktop-app", "typescript", "monorepo"], "author": "Your Name <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/your-username/book-editor/issues"}, "homepage": "https://github.com/your-username/book-editor#readme"}