/**
 * 编辑器专用样式文件
 * 确保与现有 Ant Design 样式兼容，避免冲突
 */

/* 编辑器容器 */
.book-editor-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 编辑器工具栏 */
.book-editor-toolbar {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  z-index: 100;
}

.book-editor-toolbar .ant-btn {
  margin-right: 8px;
}

.book-editor-toolbar .ant-btn:last-child {
  margin-right: 0;
}

/* 编辑器主体布局 */
.book-editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧组件面板 */
.book-editor-sidebar-left {
  width: 280px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.book-editor-sidebar-left .ant-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.book-editor-sidebar-left .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.book-editor-sidebar-left .ant-tabs-tabpane {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

/* 右侧属性面板 */
.book-editor-sidebar-right {
  width: 320px;
  background: white;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.book-editor-sidebar-right .ant-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.book-editor-sidebar-right .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.book-editor-sidebar-right .ant-tabs-tabpane {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

/* 中央画布区域 */
.book-editor-canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f0f0f0;
  position: relative;
}

/* GrapesJS 画布容器 */
.book-editor-canvas {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 组件面板样式 */
.book-components-panel {
  height: 100%;
}

.book-component-category {
  margin-bottom: 24px;
}

.book-component-category:last-child {
  margin-bottom: 0;
}

.book-component-category-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.book-component-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.book-component-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  text-align: center;
  user-select: none;
}

.book-component-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.book-component-item:active {
  transform: translateY(0);
}

.book-component-icon {
  font-size: 20px;
  color: #1890ff;
  margin-bottom: 8px;
  display: block;
}

.book-component-label {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
}

/* 属性面板样式 */
.book-properties-panel {
  height: 100%;
}

.book-property-group {
  margin-bottom: 24px;
}

.book-property-group:last-child {
  margin-bottom: 0;
}

.book-property-group-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.book-property-item {
  margin-bottom: 16px;
}

.book-property-item:last-child {
  margin-bottom: 0;
}

.book-property-label {
  font-size: 13px;
  color: #595959;
  margin-bottom: 6px;
  display: block;
  font-weight: 500;
}

/* 样式管理器容器 */
.styles-container {
  height: 100%;
  overflow-y: auto;
}

.traits-container {
  height: 100%;
  overflow-y: auto;
}

.layers-container {
  height: 100%;
  overflow-y: auto;
}

.blocks-container {
  height: 100%;
  overflow-y: auto;
}

/* GrapesJS 样式覆盖 */
.gjs-editor {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.gjs-cv-canvas {
  background: white;
  border-radius: 8px;
  margin: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.gjs-frame {
  border: none;
  border-radius: 8px;
}

/* 组件高亮样式 */
.gjs-selected {
  outline: 2px solid #1890ff !important;
  outline-offset: -2px;
}

.gjs-hovered {
  outline: 1px dashed #1890ff !important;
  outline-offset: -1px;
}

/* 拖拽辅助样式 */
.gjs-dashed {
  border: 2px dashed #1890ff !important;
  background: rgba(24, 144, 255, 0.05) !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .book-editor-sidebar-left {
    width: 240px;
  }
  
  .book-editor-sidebar-right {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .book-editor-main {
    flex-direction: column;
  }
  
  .book-editor-sidebar-left,
  .book-editor-sidebar-right {
    width: 100%;
    height: 200px;
    border: none;
    border-top: 1px solid #e8e8e8;
  }
  
  .book-component-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 加载状态 */
.book-editor-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

/* 错误状态 */
.book-editor-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ff4d4f;
  text-align: center;
}

.book-editor-error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.book-editor-error-message {
  font-size: 16px;
  margin-bottom: 8px;
}

.book-editor-error-description {
  font-size: 14px;
  color: #8c8c8c;
}

/* 自定义滚动条 */
.book-editor-sidebar-left .ant-tabs-tabpane::-webkit-scrollbar,
.book-editor-sidebar-right .ant-tabs-tabpane::-webkit-scrollbar,
.styles-container::-webkit-scrollbar,
.traits-container::-webkit-scrollbar,
.layers-container::-webkit-scrollbar,
.blocks-container::-webkit-scrollbar {
  width: 6px;
}

.book-editor-sidebar-left .ant-tabs-tabpane::-webkit-scrollbar-track,
.book-editor-sidebar-right .ant-tabs-tabpane::-webkit-scrollbar-track,
.styles-container::-webkit-scrollbar-track,
.traits-container::-webkit-scrollbar-track,
.layers-container::-webkit-scrollbar-track,
.blocks-container::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.book-editor-sidebar-left .ant-tabs-tabpane::-webkit-scrollbar-thumb,
.book-editor-sidebar-right .ant-tabs-tabpane::-webkit-scrollbar-thumb,
.styles-container::-webkit-scrollbar-thumb,
.traits-container::-webkit-scrollbar-thumb,
.layers-container::-webkit-scrollbar-thumb,
.blocks-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.book-editor-sidebar-left .ant-tabs-tabpane::-webkit-scrollbar-thumb:hover,
.book-editor-sidebar-right .ant-tabs-tabpane::-webkit-scrollbar-thumb:hover,
.styles-container::-webkit-scrollbar-thumb:hover,
.traits-container::-webkit-scrollbar-thumb:hover,
.layers-container::-webkit-scrollbar-thumb:hover,
.blocks-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
