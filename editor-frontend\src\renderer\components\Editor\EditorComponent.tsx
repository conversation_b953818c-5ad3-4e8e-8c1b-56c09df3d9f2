/**
 * 主编辑器组件
 * 使用 @grapesjs/react 实现的图书编辑器
 */

import GjsEditor, { Canvas, WithEditor } from '@grapesjs/react';
import { message } from 'antd';
import type { Editor } from 'grapesjs';
import grapesjs from 'grapesjs';
import React, { useCallback, useEffect, useMemo } from 'react';

import { getEditorConfig } from '../../../config/editorConfig';
import { useEditor } from '../../../hooks/useEditor';
import { allBookComponents } from './bookComponents';
import ComponentPanel from './ComponentPanel';
import EditorToolbar from './EditorToolbar';
import PropertiesPanel from './PropertiesPanel';

import 'grapesjs/dist/css/grapes.min.css';
import '../../../styles/editorStyles.css';

/**
 * 编辑器组件属性接口
 */
export interface EditorComponentProps {
  /** 编辑器高度 */
  height?: string | number;
  /** 初始内容 */
  initialContent?: {
    html?: string;
    css?: string;
    components?: any;
    styles?: any;
  };
  /** 保存回调 */
  onSave?: (content: { html: string; css: string }) => void;
  /** 加载回调 */
  onLoad?: () => void;
  /** 错误回调 */
  onError?: (error: Error) => void;
}

/**
 * 主编辑器组件
 */
const EditorComponent: React.FC<EditorComponentProps> = ({
  height = '100vh',
  initialContent,
  onSave,
  onLoad,
  onError,
}) => {
  const { editor, state, actions, setEditor } = useEditor();

  /**
   * 编辑器配置
   */
  const editorConfig = useMemo(() => {
    return getEditorConfig({
      height: typeof height === 'number' ? `${height}px` : height,
      // 禁用默认面板，使用自定义 UI
      panels: { defaults: [] },
      // 禁用默认块管理器，使用自定义组件面板
      blockManager: { appendTo: null },
      // 配置存储管理器
      storageManager: {
        type: 'local',
        autosave: false,
        autoload: false,
      },
    });
  }, [height]);

  /**
   * 编辑器初始化回调
   */
  const handleEditorInit = useCallback(
    (editorInstance: Editor) => {
      try {
        console.log('编辑器初始化:', editorInstance);

        // 设置编辑器实例
        setEditor(editorInstance);

        // 添加图书组件到块管理器
        const blockManager = editorInstance.BlockManager;
        allBookComponents.forEach(component => {
          blockManager.add(component.id, component);
        });

        // 加载初始内容
        if (initialContent) {
          actions.loadContent(initialContent);
        } else {
          // 如果没有初始内容，设置一些默认内容
          editorInstance.setComponents(`
            <div style="padding: 40px; max-width: 800px; margin: 0 auto;">
              <h1 style="color: #1a1a1a; margin-bottom: 20px;">欢迎使用图书编辑器</h1>
              <p style="color: #4a4a4a; line-height: 1.8; margin-bottom: 20px;">
                这是一个基于 GrapesJS React 的图书编辑器。您可以从左侧面板拖拽组件到这里，
                然后在右侧面板中编辑组件的属性和样式。
              </p>
            </div>
          `);
        }

        // 调用加载回调
        onLoad?.();

        message.success('编辑器初始化成功');
      } catch (error) {
        console.error('编辑器初始化失败:', error);
        const errorObj = error instanceof Error ? error : new Error('编辑器初始化失败');
        onError?.(errorObj);
        message.error('编辑器初始化失败');
      }
    },
    [setEditor, actions, initialContent, onLoad, onError]
  );

  /**
   * 保存处理
   */
  const handleSave = useCallback(async () => {
    try {
      await actions.saveContent();
      const content = actions.exportContent();
      onSave?.(content);
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  }, [actions, onSave]);

  /**
   * 监听键盘快捷键
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+S 保存
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        handleSave();
      }
      // Ctrl+Z 撤销
      else if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
        event.preventDefault();
        actions.undo();
      }
      // Ctrl+Shift+Z 或 Ctrl+Y 重做
      else if (
        (event.ctrlKey && event.shiftKey && event.key === 'Z') ||
        (event.ctrlKey && event.key === 'y')
      ) {
        event.preventDefault();
        actions.redo();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [actions, handleSave]);

  // 移除加载状态检查，直接渲染编辑器

  return (
    <div className='book-editor-container'>
      {/* 编辑器工具栏 */}
      <EditorToolbar editor={editor} state={state} actions={actions} onSave={handleSave} />

      {/* 编辑器主体 */}
      <div className='book-editor-main'>
        {/* 左侧组件面板 */}
        <div className='book-editor-sidebar-left'>
          <ComponentPanel editor={editor} />
        </div>

        {/* 中央画布区域 */}
        <div className='book-editor-canvas-area'>
          <GjsEditor
            grapesjs={grapesjs}
            grapesjsCss='https://unpkg.com/grapesjs/dist/css/grapes.min.css'
            options={editorConfig}
            onEditor={handleEditorInit}
          >
            <div className='book-editor-canvas'>
              <Canvas />
            </div>
          </GjsEditor>
        </div>

        {/* 右侧属性面板 */}
        <div className='book-editor-sidebar-right'>
          <WithEditor>
            <PropertiesPanel />
          </WithEditor>
        </div>
      </div>
    </div>
  );
};

export default React.memo(EditorComponent);
