import { CloseOutlined, CompressOutlined, ExpandOutlined, MinusOutlined } from '@ant-design/icons';
import { Button, Space, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';

import type { WindowState } from '@shared/types';
import { useElectronAPI } from '../hooks/useElectronAPI';

export const WindowControls: React.FC = () => {
  const [windowState, setWindowState] = useState<WindowState | null>(null);
  const electronAPI = useElectronAPI();

  useEffect(() => {
    const updateWindowState = async () => {
      if (electronAPI) {
        try {
          const state = await electronAPI.window.getState();
          setWindowState(state);
        } catch (error) {
          console.error('Failed to get window state:', error);
        }
      }
    };

    updateWindowState();

    // 监听窗口状态变化
    if (electronAPI) {
      electronAPI.window.onMaximize(updateWindowState);
      electronAPI.window.onUnmaximize(updateWindowState);
      electronAPI.window.onMinimize(updateWindowState);
      electronAPI.window.onRestore(updateWindowState);
    }
  }, [electronAPI]);

  const handleMinimize = () => {
    electronAPI?.window.minimize();
  };

  const handleMaximize = () => {
    if (windowState?.isMaximized) {
      electronAPI?.window.unmaximize();
    } else {
      electronAPI?.window.maximize();
    }
  };

  const handleClose = () => {
    electronAPI?.window.close();
  };

  // 如果不在 Electron 环境中，不显示窗口控制按钮
  if (!electronAPI) {
    return null;
  }

  return (
    <Space size={4}>
      <Tooltip title='最小化'>
        <Button
          type='text'
          size='small'
          icon={<MinusOutlined />}
          onClick={handleMinimize}
          style={{
            color: 'white',
            border: 'none',
            width: '32px',
            height: '32px',
          }}
          onMouseEnter={e => {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        />
      </Tooltip>

      <Tooltip title={windowState?.isMaximized ? '还原' : '最大化'}>
        <Button
          type='text'
          size='small'
          icon={windowState?.isMaximized ? <CompressOutlined /> : <ExpandOutlined />}
          onClick={handleMaximize}
          style={{
            color: 'white',
            border: 'none',
            width: '32px',
            height: '32px',
          }}
          onMouseEnter={e => {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        />
      </Tooltip>

      <Tooltip title='关闭'>
        <Button
          type='text'
          size='small'
          icon={<CloseOutlined />}
          onClick={handleClose}
          style={{
            color: 'white',
            border: 'none',
            width: '32px',
            height: '32px',
          }}
          onMouseEnter={e => {
            e.currentTarget.style.backgroundColor = '#e81123';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        />
      </Tooltip>
    </Space>
  );
};
