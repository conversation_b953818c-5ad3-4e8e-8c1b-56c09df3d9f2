/**
 * 简化的编辑器测试组件
 * 用于诊断 @grapesjs/react 集成问题
 */

import React, { useCallback } from 'react';
import { message } from 'antd';
import grapesjs from 'grapesjs';
import GjsEditor, { Canvas } from '@grapesjs/react';
import type { Editor } from 'grapesjs';

import 'grapesjs/dist/css/grapes.min.css';

/**
 * 简化的编辑器测试组件
 */
const SimpleEditorTest: React.FC = () => {
  /**
   * 编辑器初始化回调
   */
  const handleEditorInit = useCallback((editor: Editor) => {
    console.log('简化编辑器初始化成功:', editor);
    message.success('简化编辑器初始化成功');
    
    // 添加一些基本组件
    const blockManager = editor.BlockManager;
    
    // 添加文本组件
    blockManager.add('text-block', {
      label: '文本',
      content: '<div>这是一个文本块</div>',
      category: '基础',
    });
    
    // 添加标题组件
    blockManager.add('heading-block', {
      label: '标题',
      content: '<h1>这是标题</h1>',
      category: '基础',
    });
    
    // 设置一些初始内容
    editor.setComponents(`
      <div style="padding: 20px;">
        <h1>测试标题</h1>
        <p>这是测试内容</p>
      </div>
    `);
  }, []);

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '10px', background: '#f0f0f0', borderBottom: '1px solid #ddd' }}>
        <h3 style={{ margin: 0 }}>简化编辑器测试</h3>
      </div>
      
      <div style={{ flex: 1, display: 'flex' }}>
        {/* 左侧面板 */}
        <div style={{ width: '200px', background: '#fafafa', borderRight: '1px solid #ddd', padding: '10px' }}>
          <h4>组件面板</h4>
          <p>组件将显示在这里</p>
        </div>
        
        {/* 中央画布 */}
        <div style={{ flex: 1, position: 'relative' }}>
          <GjsEditor
            grapesjs={grapesjs}
            grapesjsCss="https://unpkg.com/grapesjs/dist/css/grapes.min.css"
            options={{
              height: '100%',
              width: '100%',
              storageManager: false,
              blockManager: {
                appendTo: '.blocks-container',
              },
              panels: {
                defaults: [
                  {
                    id: 'basic-actions',
                    el: '.panel-basic-actions',
                    buttons: [
                      {
                        id: 'visibility',
                        active: true,
                        className: 'btn-toggle-borders',
                        label: '<i class="fa fa-clone"></i>',
                        command: 'sw-visibility',
                      },
                    ],
                  },
                ],
              },
            }}
            onEditor={handleEditorInit}
          >
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <div className="panel-basic-actions" style={{ padding: '10px', background: '#fff', borderBottom: '1px solid #ddd' }}>
                工具栏
              </div>
              <div style={{ flex: 1 }}>
                <Canvas />
              </div>
            </div>
          </GjsEditor>
        </div>
        
        {/* 右侧面板 */}
        <div style={{ width: '200px', background: '#fafafa', borderLeft: '1px solid #ddd', padding: '10px' }}>
          <h4>属性面板</h4>
          <p>属性将显示在这里</p>
        </div>
      </div>
      
      {/* 组件容器 */}
      <div className="blocks-container" style={{ display: 'none' }}></div>
    </div>
  );
};

export default SimpleEditorTest;
