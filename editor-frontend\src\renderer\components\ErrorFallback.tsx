import { BugOutlined, HomeOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Card, Result, Space, Typography } from 'antd';
import React from 'react';

const { Paragraph, Text } = Typography;

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  const handleReload = () => {
    // 重新加载应用
    if (window.electronAPI) {
      window.electronAPI.app.restart();
    } else {
      window.location.reload();
    }
  };

  const handleReset = () => {
    resetErrorBoundary();
  };

  const handleReportError = () => {
    // 这里可以实现错误报告功能
    if (window.electronAPI) {
      window.electronAPI.dialog.showMessage({
        type: 'info',
        title: '错误报告',
        message: '错误报告功能开发中...',
        buttons: ['确定'],
      });
    }
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#f0f2f5',
        padding: '24px',
      }}
    >
      <Card style={{ maxWidth: '600px', width: '100%' }}>
        <Result
          status='error'
          icon={<BugOutlined style={{ color: '#ff4d4f' }} />}
          title='应用发生错误'
          subTitle='很抱歉，应用遇到了一个意外错误。您可以尝试重新加载或重启应用。'
          extra={
            <Space direction='vertical' style={{ width: '100%' }}>
              <Space>
                <Button type='primary' icon={<ReloadOutlined />} onClick={handleReload}>
                  重新加载应用
                </Button>
                <Button icon={<HomeOutlined />} onClick={handleReset}>
                  返回主页
                </Button>
                <Button onClick={handleReportError}>报告错误</Button>
              </Space>
            </Space>
          }
        />

        <Card
          title='错误详情'
          size='small'
          style={{ marginTop: '16px' }}
          styles={{ body: { maxHeight: '200px', overflow: 'auto' } }}
        >
          <Paragraph>
            <Text strong>错误消息：</Text>
            <br />
            <Text code>{error.message}</Text>
          </Paragraph>

          {error.stack && (
            <Paragraph>
              <Text strong>错误堆栈：</Text>
              <br />
              <pre
                style={{
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  maxHeight: '150px',
                }}
              >
                {error.stack}
              </pre>
            </Paragraph>
          )}
        </Card>

        <Card title='故障排除建议' size='small' style={{ marginTop: '16px' }}>
          <ul>
            <li>尝试重新加载应用</li>
            <li>检查网络连接是否正常</li>
            <li>确保应用具有必要的文件访问权限</li>
            <li>如果问题持续存在，请重启应用</li>
            <li>联系技术支持并提供错误详情</li>
          </ul>
        </Card>
      </Card>
    </div>
  );
};
