# 新图书编辑器使用说明

## 概述

新图书编辑器基于 @grapesjs/react 构建，提供了更好的 React 集成和更现代的架构。

## 功能特性

### 🎨 可视化编辑
- 拖拽式组件添加
- 实时预览
- 所见即所得编辑

### 📚 图书专用组件
- **标题组件**：H1-H6，支持样式自定义
- **段落组件**：支持富文本格式，包括引言段落
- **引用组件**：块引用和拉引用样式
- **图片组件**：支持上传、调整大小、对齐
- **章节分隔符**：章节分隔、节分隔、分页符
- **列表组件**：有序和无序列表

### 🛠️ 编辑工具
- **撤销/重做**：支持操作历史管理
- **保存/加载**：内容持久化
- **预览模式**：实时预览最终效果
- **设备切换**：桌面、平板、手机视图
- **导入/导出**：JSON 格式内容交换

### 🎯 界面布局
- **左侧面板**：组件库，按分类组织
- **中央画布**：可视化编辑区域
- **右侧面板**：属性编辑、样式管理、图层管理
- **顶部工具栏**：常用操作快捷入口

## 使用方法

### 启动编辑器

1. 启动开发服务器：
   ```bash
   cd editor-frontend
   npm run dev
   ```

2. 打开浏览器访问：http://localhost:5173

3. 点击"新图书编辑器"按钮

### 基本操作

#### 添加组件
1. 从左侧组件面板选择需要的组件
2. 点击或拖拽到中央画布
3. 组件会自动添加到画布中心

#### 编辑组件
1. 点击画布中的组件进行选择
2. 在右侧属性面板中编辑组件属性
3. 在样式面板中调整组件样式
4. 修改会实时反映在画布中

#### 保存和加载
- **保存**：点击工具栏的保存按钮或使用 Ctrl+S
- **导出**：通过"更多"菜单导出 JSON 格式内容
- **导入**：通过"更多"菜单导入之前保存的内容

### 快捷键

- `Ctrl+S`：保存内容
- `Ctrl+Z`：撤销操作
- `Ctrl+Y` 或 `Ctrl+Shift+Z`：重做操作

## 技术架构

### 核心组件

- **EditorComponent**：主编辑器组件，使用 @grapesjs/react
- **EditorToolbar**：工具栏，提供常用操作
- **ComponentPanel**：组件面板，展示可用组件
- **PropertiesPanel**：属性面板，编辑选中组件

### 状态管理

- **useEditor Hook**：编辑器状态管理
- **防抖保存**：自动保存机制
- **撤销重做**：操作历史管理

### 样式系统

- **CSS Modules**：避免样式冲突
- **Ant Design 集成**：保持 UI 一致性
- **响应式设计**：适配不同屏幕尺寸

## 开发指南

### 添加新组件

1. 在 `bookComponents.ts` 中定义组件：
   ```typescript
   export const newComponent: BlockProperties = {
     id: 'new-component',
     label: '新组件',
     category: BOOK_CATEGORIES.TEXT,
     content: {
       type: 'text',
       tagName: 'div',
       content: '新组件内容',
       style: { /* 默认样式 */ },
       traits: [ /* 可编辑属性 */ ],
     },
   };
   ```

2. 将组件添加到相应分类中

### 自定义样式

1. 在 `editorStyles.css` 中添加样式
2. 使用 BEM 命名规范
3. 确保与 Ant Design 样式兼容

### 扩展功能

1. 在 `useEditor.ts` 中添加新的状态和操作
2. 在相应组件中使用新功能
3. 添加适当的错误处理

## 故障排除

### 常见问题

1. **编辑器无法加载**
   - 检查控制台错误信息
   - 确认所有依赖已正确安装
   - 验证 @grapesjs/react 版本兼容性

2. **组件无法拖拽**
   - 检查组件定义是否正确
   - 确认块管理器已正确初始化
   - 验证拖拽事件处理

3. **样式不生效**
   - 检查 CSS 文件是否正确导入
   - 确认样式选择器优先级
   - 验证 CSS Modules 配置

### 调试技巧

1. 使用浏览器开发者工具
2. 检查 React DevTools
3. 查看 GrapesJS 编辑器实例状态
4. 启用详细日志输出

## 性能优化

### 建议

1. 使用 React.memo 优化组件渲染
2. 实现虚拟滚动（大量组件时）
3. 优化图片资源加载
4. 使用防抖处理频繁操作

### 监控

1. 监控内存使用情况
2. 检查组件渲染次数
3. 分析包大小和加载时间

## 更新日志

### v1.0.0 (2025-08-21)
- ✅ 完成基础架构搭建
- ✅ 实现图书专用组件库
- ✅ 集成 @grapesjs/react
- ✅ 添加状态管理和持久化
- ✅ 实现响应式界面布局
- ✅ 添加完整的工具栏功能

## 后续计划

- [ ] 添加更多图书专用组件
- [ ] 实现协作编辑功能
- [ ] 添加模板系统
- [ ] 集成 AI 辅助功能
- [ ] 优化移动端体验
