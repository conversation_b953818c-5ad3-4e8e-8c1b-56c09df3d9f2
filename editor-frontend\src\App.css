/* Import global styles */
@import './styles/global.scss';

/* Application specific styles */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: #001529;
  color: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.app-header .logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-decoration: none;
}

.app-header .logo:hover {
  color: #40a9ff;
}

.app-content {
  flex: 1;
  padding: 24px;
  background: #f0f2f5;
  overflow-y: auto;
}

.app-footer {
  background: #f0f2f5;
  text-align: center;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  color: rgba(0, 0, 0, 0.65);
}

/* Welcome page styles */
.welcome-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 48px 24px;
  text-align: center;
}

.welcome-title {
  font-size: 48px;
  font-weight: 700;
  color: #001529;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 32px;
  line-height: 1.6;
}

.welcome-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 48px;
}

.feature-card {
  background: white;
  padding: 32px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.feature-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.feature-title {
  font-size: 20px;
  font-weight: 600;
  color: #001529;
  margin-bottom: 12px;
}

.feature-description {
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.6;
}

.tech-stack {
  margin-top: 64px;
  padding: 48px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tech-stack-title {
  font-size: 24px;
  font-weight: 600;
  color: #001529;
  margin-bottom: 32px;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
}

.tech-category {
  text-align: left;
}

.tech-category-title {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tech-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tech-list li {
  padding: 8px 0;
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tech-list li:last-child {
  border-bottom: none;
}

.tech-version {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .welcome-title {
    font-size: 36px;
  }

  .welcome-subtitle {
    font-size: 18px;
  }

  .welcome-features {
    grid-template-columns: 1fr;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .app-content {
    padding: 16px;
  }

  .welcome-container {
    padding: 24px 16px;
  }
}

/* Loading and transition effects */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-out;
}
