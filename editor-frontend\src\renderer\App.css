/* Renderer App 特定样式 */

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: #001529;
  color: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  -webkit-app-region: drag; /* 允许拖拽窗口 */
}

.app-header .logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-decoration: none;
  -webkit-app-region: no-drag; /* 禁止拖拽 */
}

.app-header .window-controls {
  -webkit-app-region: no-drag; /* 禁止拖拽 */
}

.app-content {
  flex: 1;
  padding: 24px;
  background: #f0f2f5;
  overflow-y: auto;
}

.app-footer {
  background: #f0f2f5;
  text-align: center;
  padding: 12px 24px;
  border-top: 1px solid #f0f0f0;
  color: rgba(0, 0, 0, 0.65);
}

/* 欢迎页面样式 */
.welcome-container {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  text-align: center;
  margin-bottom: 24px;
}

.welcome-title {
  background: linear-gradient(135deg, #1890ff, #722ed1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* 系统信息卡片 */
.info-card {
  height: 100%;
}

.info-card .ant-card-body {
  padding: 16px;
}

.info-card p {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card p:last-child {
  margin-bottom: 0;
}

/* IPC 测试面板样式 */
.ipc-test-panel {
  margin-top: 24px;
}

.test-results {
  height: 300px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.test-results::-webkit-scrollbar {
  width: 6px;
}

.test-results::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.test-results::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.test-results::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 窗口控制按钮样式 */
.window-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.window-control-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.window-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.window-control-btn.close:hover {
  background-color: #e81123;
}

/* 错误边界样式 */
.error-fallback {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  padding: 24px;
}

.error-details {
  max-height: 200px;
  overflow: auto;
}

.error-stack {
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow: auto;
  max-height: 150px;
  white-space: pre-wrap;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }
  
  .app-header .logo {
    font-size: 16px;
  }
  
  .app-content {
    padding: 16px;
  }
  
  .welcome-container {
    padding: 0;
  }
  
  .window-controls {
    display: none; /* 在小屏幕上隐藏窗口控制 */
  }
}

@media (max-width: 576px) {
  .app-header {
    padding: 0 12px;
  }
  
  .app-content {
    padding: 12px;
  }
  
  .info-card p {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  flex-direction: column;
  gap: 16px;
}

/* 卡片悬停效果 */
.hover-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.online {
  background-color: #52c41a;
}

.status-indicator.offline {
  background-color: #ff4d4f;
}

.status-indicator.warning {
  background-color: #faad14;
}
