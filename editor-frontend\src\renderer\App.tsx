import {
  AndroidOutlined,
  AppleOutlined,
  BookOutlined,
  BugOutlined,
  EditOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  WindowsOutlined,
} from '@ant-design/icons';
import { Button, Card, Col, ConfigProvider, Layout, message, Row, Space, Typography } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import React, { useEffect, useState } from 'react';

import type { AppInfo } from '../shared/types';
import { IPCTestPanel } from './components/IPCTestPanel';
import { WindowControls } from './components/WindowControls';
import { useElectronAPI } from './hooks/useElectronAPI';
import { EditorPage } from './pages/EditorPage';
import NewEditorPage from './pages/NewEditorPage';

import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

const App: React.FC = () => {
  const [appInfo, setAppInfo] = useState<AppInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState<'home' | 'editor' | 'new-editor' | 'simple-test'>(
    'home'
  );
  const electronAPI = useElectronAPI();

  useEffect(() => {
    const loadAppInfo = async () => {
      try {
        if (electronAPI) {
          const info = await electronAPI.app.getInfo();
          setAppInfo(info);
        }
      } catch (error) {
        console.error('Failed to load app info:', error);
        message.error('加载应用信息失败');
      } finally {
        setIsLoading(false);
      }
    };

    loadAppInfo();
  }, [electronAPI]);

  const handleNewBook = () => {
    setCurrentPage('editor');
    message.success('已进入编辑器');
  };

  const handleOpenBook = () => {
    setCurrentPage('editor');
    message.success('已进入编辑器');
  };

  const handleOpenEditor = () => {
    setCurrentPage('editor');
  };

  const handleOpenNewEditor = () => {
    setCurrentPage('new-editor');
  };

  const handleOpenSimpleTest = () => {
    setCurrentPage('simple-test');
  };

  const handleBackToHome = () => {
    setCurrentPage('home');
  };

  const handleSettings = () => {
    message.info('设置功能开发中...');
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'win32':
        return <WindowsOutlined />;
      case 'darwin':
        return <AppleOutlined />;
      case 'linux':
        return <AndroidOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  if (isLoading) {
    return (
      <ConfigProvider locale={zhCN}>
        <Layout style={{ minHeight: '100vh' }}>
          <Content style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Text>加载中...</Text>
          </Content>
        </Layout>
      </ConfigProvider>
    );
  }

  // 如果当前页面是编辑器，显示编辑器页面
  if (currentPage === 'editor') {
    return <EditorPage onBack={handleBackToHome} />;
  }

  // 如果当前页面是新编辑器，显示新编辑器页面
  if (currentPage === 'new-editor') {
    return <NewEditorPage />;
  }

  // 如果当前页面是简化测试，显示简化测试页面
  if (currentPage === 'simple-test') {
    return <SimpleEditorTestPage />;
  }

  return (
    <ConfigProvider locale={zhCN}>
      <Layout style={{ minHeight: '100vh' }}>
        <Header
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            background: '#001529',
            padding: '0 24px',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <BookOutlined style={{ fontSize: '24px', color: 'white', marginRight: '12px' }} />
            <Title level={3} style={{ color: 'white', margin: 0 }}>
              桌面端图书编辑器
            </Title>
          </div>
          <WindowControls />
        </Header>

        <Content style={{ padding: '24px', background: '#f0f2f5' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            {/* 欢迎区域 */}
            <Card style={{ marginBottom: '24px', textAlign: 'center' }}>
              <Title level={2}>欢迎使用图书编辑器 {appInfo?.version}</Title>
              <Paragraph>
                这是一个基于 Electron、React 和 Django 构建的现代化桌面图书编辑器应用。
              </Paragraph>
              <Space size='large' wrap>
                <Button
                  type='primary'
                  size='large'
                  icon={<EditOutlined />}
                  onClick={handleOpenSimpleTest}
                >
                  简化编辑器测试
                </Button>
                <Button size='large' icon={<EditOutlined />} onClick={handleOpenNewEditor}>
                  新图书编辑器
                </Button>
                <Button size='large' icon={<EditOutlined />} onClick={handleOpenEditor}>
                  旧版编辑器
                </Button>
                <Button size='large' icon={<BookOutlined />} onClick={handleNewBook}>
                  创建新图书
                </Button>
                <Button size='large' onClick={handleOpenBook}>
                  打开图书
                </Button>
                <Button icon={<SettingOutlined />} onClick={handleSettings}>
                  设置
                </Button>
              </Space>
            </Card>

            {/* 系统信息 */}
            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col xs={24} sm={12} md={8}>
                <Card title='应用信息' size='small'>
                  <p>
                    <strong>名称：</strong> {appInfo?.name}
                  </p>
                  <p>
                    <strong>版本：</strong> {appInfo?.version}
                  </p>
                  <p>
                    <strong>平台：</strong> {getPlatformIcon(appInfo?.platform || '')}{' '}
                    {appInfo?.platform}
                  </p>
                  <p>
                    <strong>架构：</strong> {appInfo?.arch}
                  </p>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card title='运行环境' size='small'>
                  <p>
                    <strong>Electron：</strong> {appInfo?.electronVersion}
                  </p>
                  <p>
                    <strong>Chrome：</strong> {appInfo?.chromeVersion}
                  </p>
                  <p>
                    <strong>Node.js：</strong> {appInfo?.nodeVersion}
                  </p>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Card title='技术栈' size='small'>
                  <p>
                    <strong>前端：</strong> React 18.x + TypeScript
                  </p>
                  <p>
                    <strong>构建：</strong> Vite + Electron
                  </p>
                  <p>
                    <strong>UI：</strong> Ant Design 5.x
                  </p>
                  <p>
                    <strong>后端：</strong> Django + DRF
                  </p>
                </Card>
              </Col>
            </Row>

            {/* IPC 通信测试面板 */}
            <Card
              title={
                <>
                  <BugOutlined /> IPC 通信测试
                </>
              }
              style={{ marginBottom: '24px' }}
            >
              <IPCTestPanel />
            </Card>
          </div>
        </Content>

        <Footer style={{ textAlign: 'center', background: '#f0f2f5' }}>
          桌面端图书编辑器 ©2024 Created by Book Editor Team
        </Footer>
      </Layout>
    </ConfigProvider>
  );
};

export default App;
