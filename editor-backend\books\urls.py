from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'books'

router = DefaultRouter()
router.register(r'books', views.BookViewSet)
router.register(r'chapters', views.ChapterViewSet)

urlpatterns = [
    path('', include(router.urls)),
    
    # Additional endpoints
    path('books/<int:book_id>/chapters/', views.BookChaptersView.as_view(), name='book-chapters'),
    path('books/<int:book_id>/export/', views.BookExportView.as_view(), name='book-export'),
]
