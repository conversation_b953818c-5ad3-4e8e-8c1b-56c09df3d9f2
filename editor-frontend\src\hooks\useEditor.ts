/**
 * 编辑器状态管理 Hook
 * 提供编辑器实例管理、内容保存加载、撤销重做等功能
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import type { Editor } from 'grapesjs';
import { message } from 'antd';

/**
 * 编辑器内容接口
 */
export interface EditorContent {
  html: string;
  css: string;
  components?: any;
  styles?: any;
}

/**
 * 编辑器状态接口
 */
export interface EditorState {
  isLoading: boolean;
  hasUnsavedChanges: boolean;
  selectedComponent: any | null;
  currentFile: string | null;
  canUndo: boolean;
  canRedo: boolean;
}

/**
 * 编辑器操作接口
 */
export interface EditorActions {
  saveContent: () => Promise<void>;
  loadContent: (content: EditorContent) => void;
  exportContent: () => EditorContent;
  clearContent: () => void;
  undo: () => void;
  redo: () => void;
  setCurrentFile: (filePath: string | null) => void;
  markAsChanged: () => void;
  markAsSaved: () => void;
}

/**
 * Hook 返回值接口
 */
export interface UseEditorReturn {
  editor: Editor | null;
  state: EditorState;
  actions: EditorActions;
  setEditor: (editor: Editor | null) => void;
}

/**
 * 防抖保存配置
 */
const AUTOSAVE_DELAY = 2000; // 2秒

/**
 * 编辑器状态管理 Hook
 */
export const useEditor = (): UseEditorReturn => {
  // 编辑器实例
  const [editor, setEditor] = useState<Editor | null>(null);
  
  // 编辑器状态
  const [state, setState] = useState<EditorState>({
    isLoading: true,
    hasUnsavedChanges: false,
    selectedComponent: null,
    currentFile: null,
    canUndo: false,
    canRedo: false,
  });

  // 防抖保存定时器
  const autosaveTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 最后保存的内容哈希
  const lastSavedHashRef = useRef<string>('');

  /**
   * 更新状态的辅助函数
   */
  const updateState = useCallback((updates: Partial<EditorState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * 获取内容哈希
   */
  const getContentHash = useCallback((content: EditorContent): string => {
    return btoa(JSON.stringify(content));
  }, []);

  /**
   * 导出编辑器内容
   */
  const exportContent = useCallback((): EditorContent => {
    if (!editor) {
      return { html: '', css: '' };
    }

    try {
      const html = editor.getHtml();
      const css = editor.getCss();
      const components = editor.getComponents();
      const styles = editor.getStyle();

      return {
        html,
        css,
        components: components.toJSON(),
        styles: styles.toJSON(),
      };
    } catch (error) {
      console.error('导出内容失败:', error);
      message.error('导出内容失败');
      return { html: '', css: '' };
    }
  }, [editor]);

  /**
   * 加载内容到编辑器
   */
  const loadContent = useCallback((content: EditorContent) => {
    if (!editor) return;

    try {
      updateState({ isLoading: true });

      // 清空现有内容
      editor.setComponents('');
      editor.setStyle('');

      // 加载新内容
      if (content.components) {
        editor.loadProjectData({
          components: content.components,
          styles: content.styles || [],
        });
      } else if (content.html || content.css) {
        editor.setComponents(content.html || '');
        editor.setStyle(content.css || '');
      }

      // 更新保存状态
      const hash = getContentHash(content);
      lastSavedHashRef.current = hash;
      
      updateState({
        isLoading: false,
        hasUnsavedChanges: false,
      });

      message.success('内容加载成功');
    } catch (error) {
      console.error('加载内容失败:', error);
      message.error('加载内容失败');
      updateState({ isLoading: false });
    }
  }, [editor, updateState, getContentHash]);

  /**
   * 保存内容
   */
  const saveContent = useCallback(async (): Promise<void> => {
    if (!editor) return;

    try {
      const content = exportContent();
      const hash = getContentHash(content);

      // 如果内容没有变化，跳过保存
      if (hash === lastSavedHashRef.current) {
        return;
      }

      // 这里可以添加实际的保存逻辑，比如调用 Electron API
      // await electronAPI.saveFile(state.currentFile, content);

      lastSavedHashRef.current = hash;
      updateState({ hasUnsavedChanges: false });
      
      message.success('保存成功');
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  }, [editor, exportContent, getContentHash, updateState]);

  /**
   * 清空内容
   */
  const clearContent = useCallback(() => {
    if (!editor) return;

    try {
      editor.setComponents('');
      editor.setStyle('');
      
      lastSavedHashRef.current = '';
      updateState({
        hasUnsavedChanges: false,
        selectedComponent: null,
      });

      message.success('内容已清空');
    } catch (error) {
      console.error('清空内容失败:', error);
      message.error('清空内容失败');
    }
  }, [editor, updateState]);

  /**
   * 撤销操作
   */
  const undo = useCallback(() => {
    if (!editor) return;
    
    try {
      const undoManager = editor.UndoManager;
      if (undoManager.hasUndo()) {
        undoManager.undo();
        updateState({
          canUndo: undoManager.hasUndo(),
          canRedo: undoManager.hasRedo(),
          hasUnsavedChanges: true,
        });
      }
    } catch (error) {
      console.error('撤销失败:', error);
      message.error('撤销失败');
    }
  }, [editor, updateState]);

  /**
   * 重做操作
   */
  const redo = useCallback(() => {
    if (!editor) return;
    
    try {
      const undoManager = editor.UndoManager;
      if (undoManager.hasRedo()) {
        undoManager.redo();
        updateState({
          canUndo: undoManager.hasUndo(),
          canRedo: undoManager.hasRedo(),
          hasUnsavedChanges: true,
        });
      }
    } catch (error) {
      console.error('重做失败:', error);
      message.error('重做失败');
    }
  }, [editor, updateState]);

  /**
   * 设置当前文件
   */
  const setCurrentFile = useCallback((filePath: string | null) => {
    updateState({ currentFile: filePath });
  }, [updateState]);

  /**
   * 标记为已更改
   */
  const markAsChanged = useCallback(() => {
    updateState({ hasUnsavedChanges: true });
    
    // 设置防抖自动保存
    if (autosaveTimerRef.current) {
      clearTimeout(autosaveTimerRef.current);
    }
    
    autosaveTimerRef.current = setTimeout(() => {
      saveContent();
    }, AUTOSAVE_DELAY);
  }, [updateState, saveContent]);

  /**
   * 标记为已保存
   */
  const markAsSaved = useCallback(() => {
    updateState({ hasUnsavedChanges: false });
    
    if (autosaveTimerRef.current) {
      clearTimeout(autosaveTimerRef.current);
      autosaveTimerRef.current = null;
    }
  }, [updateState]);

  /**
   * 监听编辑器事件
   */
  useEffect(() => {
    if (!editor) return;

    // 编辑器加载完成
    const handleLoad = () => {
      updateState({ isLoading: false });
    };

    // 组件选择变化
    const handleComponentSelect = (component: any) => {
      updateState({ selectedComponent: component });
    };

    // 内容变化
    const handleContentChange = () => {
      const undoManager = editor.UndoManager;
      updateState({
        canUndo: undoManager.hasUndo(),
        canRedo: undoManager.hasRedo(),
      });
      markAsChanged();
    };

    // 绑定事件
    editor.on('load', handleLoad);
    editor.on('component:selected', handleComponentSelect);
    editor.on('component:deselected', () => updateState({ selectedComponent: null }));
    editor.on('storage:store', handleContentChange);
    editor.on('component:add', handleContentChange);
    editor.on('component:remove', handleContentChange);
    editor.on('component:update', handleContentChange);

    // 清理函数
    return () => {
      editor.off('load', handleLoad);
      editor.off('component:selected', handleComponentSelect);
      editor.off('component:deselected');
      editor.off('storage:store', handleContentChange);
      editor.off('component:add', handleContentChange);
      editor.off('component:remove', handleContentChange);
      editor.off('component:update', handleContentChange);
    };
  }, [editor, updateState, markAsChanged]);

  /**
   * 清理定时器
   */
  useEffect(() => {
    return () => {
      if (autosaveTimerRef.current) {
        clearTimeout(autosaveTimerRef.current);
      }
    };
  }, []);

  // 组装返回值
  const actions: EditorActions = {
    saveContent,
    loadContent,
    exportContent,
    clearContent,
    undo,
    redo,
    setCurrentFile,
    markAsChanged,
    markAsSaved,
  };

  return {
    editor,
    state,
    actions,
    setEditor,
  };
};
