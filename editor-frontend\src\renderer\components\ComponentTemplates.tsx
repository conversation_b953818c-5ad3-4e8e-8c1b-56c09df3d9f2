import React, { useState } from 'react';
import { Card, Tabs, Button, Space, Input, message, Modal, Typography } from 'antd';
import {
  PlusOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Search } = Input;
const { Text, Title } = Typography;

interface ComponentTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  thumbnail?: string;
  content: string;
  tags: string[];
  createdAt: Date;
}

interface ComponentTemplatesProps {
  onAddTemplate?: (template: ComponentTemplate) => void;
  onUseTemplate?: (template: ComponentTemplate) => void;
}

export const ComponentTemplates: React.FC<ComponentTemplatesProps> = ({
  onAddTemplate,
  onUseTemplate,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ComponentTemplate | null>(null);

  // 预定义模板
  const [templates, setTemplates] = useState<ComponentTemplate[]>([
    {
      id: 'hero-section',
      name: '英雄区域',
      category: '页面模板',
      description: '带有标题、描述和行动按钮的英雄区域',
      content: `<section style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 80px 20px;
        text-align: center;
        min-height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
      ">
        <div style="max-width: 800px;">
          <h1 style="font-size: 3rem; margin-bottom: 20px; font-weight: bold;">欢迎来到我们的网站</h1>
          <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">这里是一个简短的描述，介绍我们的产品或服务的核心价值。</p>
          <button style="
            background-color: #ff6b6b;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 15px;
          ">开始使用</button>
          <button style="
            background-color: transparent;
            color: white;
            border: 2px solid white;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 5px;
            cursor: pointer;
          ">了解更多</button>
        </div>
      </section>`,
      tags: ['英雄区域', '首页', '横幅'],
      createdAt: new Date(),
    },
    {
      id: 'feature-cards',
      name: '功能卡片组',
      category: '内容模板',
      description: '三列功能特性展示卡片',
      content: `<section style="padding: 60px 20px; background-color: #f8f9fa;">
        <div style="max-width: 1200px; margin: 0 auto;">
          <h2 style="text-align: center; margin-bottom: 50px; font-size: 2.5rem; color: #333;">我们的特色</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
            <div style="
              background: white;
              padding: 30px;
              border-radius: 10px;
              box-shadow: 0 5px 15px rgba(0,0,0,0.1);
              text-align: center;
              transition: transform 0.3s ease;
            ">
              <div style="
                width: 60px;
                height: 60px;
                background: #4CAF50;
                border-radius: 50%;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                color: white;
              ">🚀</div>
              <h3 style="margin-bottom: 15px; color: #333;">快速高效</h3>
              <p style="color: #666; line-height: 1.6;">我们提供快速、高效的解决方案，帮助您节省时间和成本。</p>
            </div>
            <div style="
              background: white;
              padding: 30px;
              border-radius: 10px;
              box-shadow: 0 5px 15px rgba(0,0,0,0.1);
              text-align: center;
              transition: transform 0.3s ease;
            ">
              <div style="
                width: 60px;
                height: 60px;
                background: #2196F3;
                border-radius: 50%;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                color: white;
              ">🛡️</div>
              <h3 style="margin-bottom: 15px; color: #333;">安全可靠</h3>
              <p style="color: #666; line-height: 1.6;">采用最新的安全技术，确保您的数据和隐私得到最好的保护。</p>
            </div>
            <div style="
              background: white;
              padding: 30px;
              border-radius: 10px;
              box-shadow: 0 5px 15px rgba(0,0,0,0.1);
              text-align: center;
              transition: transform 0.3s ease;
            ">
              <div style="
                width: 60px;
                height: 60px;
                background: #FF9800;
                border-radius: 50%;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                color: white;
              ">💡</div>
              <h3 style="margin-bottom: 15px; color: #333;">创新设计</h3>
              <p style="color: #666; line-height: 1.6;">独特的创新设计理念，为您带来全新的用户体验。</p>
            </div>
          </div>
        </div>
      </section>`,
      tags: ['功能', '卡片', '特性'],
      createdAt: new Date(),
    },
    {
      id: 'contact-form',
      name: '联系表单',
      category: '表单模板',
      description: '完整的联系我们表单',
      content: `<section style="padding: 60px 20px; background-color: white;">
        <div style="max-width: 600px; margin: 0 auto;">
          <h2 style="text-align: center; margin-bottom: 30px; color: #333;">联系我们</h2>
          <form style="background: #f8f9fa; padding: 40px; border-radius: 10px;">
            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">姓名</label>
              <input type="text" placeholder="请输入您的姓名" style="
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
              ">
            </div>
            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">邮箱</label>
              <input type="email" placeholder="请输入您的邮箱" style="
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
              ">
            </div>
            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">主题</label>
              <select style="
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
              ">
                <option>一般咨询</option>
                <option>技术支持</option>
                <option>商务合作</option>
                <option>其他</option>
              </select>
            </div>
            <div style="margin-bottom: 30px;">
              <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">消息</label>
              <textarea placeholder="请输入您的消息" rows="5" style="
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
                resize: vertical;
              "></textarea>
            </div>
            <button type="submit" style="
              width: 100%;
              background-color: #007bff;
              color: white;
              border: none;
              padding: 15px;
              font-size: 16px;
              border-radius: 5px;
              cursor: pointer;
            ">发送消息</button>
          </form>
        </div>
      </section>`,
      tags: ['表单', '联系', '反馈'],
      createdAt: new Date(),
    },
    {
      id: 'pricing-table',
      name: '价格表',
      category: '内容模板',
      description: '三列价格对比表',
      content: `<section style="padding: 60px 20px; background-color: #f8f9fa;">
        <div style="max-width: 1000px; margin: 0 auto;">
          <h2 style="text-align: center; margin-bottom: 50px; font-size: 2.5rem; color: #333;">选择您的方案</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
            <div style="
              background: white;
              border-radius: 10px;
              padding: 30px;
              text-align: center;
              box-shadow: 0 5px 15px rgba(0,0,0,0.1);
              position: relative;
            ">
              <h3 style="margin-bottom: 10px; color: #333;">基础版</h3>
              <div style="font-size: 3rem; font-weight: bold; color: #007bff; margin-bottom: 10px;">¥99</div>
              <p style="color: #666; margin-bottom: 30px;">每月</p>
              <ul style="list-style: none; padding: 0; margin-bottom: 30px;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 基础功能</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 5GB 存储空间</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 邮件支持</li>
              </ul>
              <button style="
                width: 100%;
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                cursor: pointer;
              ">选择方案</button>
            </div>
            <div style="
              background: white;
              border-radius: 10px;
              padding: 30px;
              text-align: center;
              box-shadow: 0 10px 30px rgba(0,0,0,0.2);
              position: relative;
              transform: scale(1.05);
              border: 3px solid #007bff;
            ">
              <div style="
                position: absolute;
                top: -15px;
                left: 50%;
                transform: translateX(-50%);
                background: #007bff;
                color: white;
                padding: 5px 20px;
                border-radius: 20px;
                font-size: 12px;
              ">推荐</div>
              <h3 style="margin-bottom: 10px; color: #333;">专业版</h3>
              <div style="font-size: 3rem; font-weight: bold; color: #007bff; margin-bottom: 10px;">¥199</div>
              <p style="color: #666; margin-bottom: 30px;">每月</p>
              <ul style="list-style: none; padding: 0; margin-bottom: 30px;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 所有基础功能</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 50GB 存储空间</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 优先支持</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 高级分析</li>
              </ul>
              <button style="
                width: 100%;
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                cursor: pointer;
              ">选择方案</button>
            </div>
            <div style="
              background: white;
              border-radius: 10px;
              padding: 30px;
              text-align: center;
              box-shadow: 0 5px 15px rgba(0,0,0,0.1);
              position: relative;
            ">
              <h3 style="margin-bottom: 10px; color: #333;">企业版</h3>
              <div style="font-size: 3rem; font-weight: bold; color: #007bff; margin-bottom: 10px;">¥399</div>
              <p style="color: #666; margin-bottom: 30px;">每月</p>
              <ul style="list-style: none; padding: 0; margin-bottom: 30px;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 所有专业功能</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 无限存储空间</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 24/7 支持</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 定制功能</li>
              </ul>
              <button style="
                width: 100%;
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                cursor: pointer;
              ">选择方案</button>
            </div>
          </div>
        </div>
      </section>`,
      tags: ['价格', '方案', '对比'],
      createdAt: new Date(),
    },
  ]);

  const categories = [
    { key: 'all', label: '全部' },
    { key: '页面模板', label: '页面模板' },
    { key: '内容模板', label: '内容模板' },
    { key: '表单模板', label: '表单模板' },
    { key: '导航模板', label: '导航模板' },
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleUseTemplate = (template: ComponentTemplate) => {
    if (onUseTemplate) {
      onUseTemplate(template);
    }
    message.success(`已添加模板：${template.name}`);
  };

  const handleCopyTemplate = (template: ComponentTemplate) => {
    navigator.clipboard.writeText(template.content);
    message.success('模板代码已复制到剪贴板');
  };

  const handleDeleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(t => t.id !== templateId));
    message.success('模板已删除');
  };

  return (
    <div style={{ padding: '16px' }}>
      <div style={{ marginBottom: '16px' }}>
        <Space style={{ marginBottom: '16px' }}>
          <Search
            placeholder="搜索模板..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsModalVisible(true)}
          >
            添加模板
          </Button>
        </Space>

        <Tabs
          activeKey={selectedCategory}
          onChange={setSelectedCategory}
          items={categories.map(cat => ({
            key: cat.key,
            label: cat.label,
          }))}
        />
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '16px',
      }}>
        {filteredTemplates.map(template => (
          <Card
            key={template.id}
            size="small"
            title={template.name}
            extra={
              <Space>
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => handleCopyTemplate(template)}
                />
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setEditingTemplate(template);
                    setIsModalVisible(true);
                  }}
                />
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteTemplate(template.id)}
                />
              </Space>
            }
            actions={[
              <Button
                key="use"
                type="primary"
                size="small"
                onClick={() => handleUseTemplate(template)}
              >
                使用模板
              </Button>
            ]}
          >
            <div style={{ marginBottom: '8px' }}>
              <Text type="secondary">{template.category}</Text>
            </div>
            <div style={{ marginBottom: '12px' }}>
              <Text>{template.description}</Text>
            </div>
            <div>
              {template.tags.map(tag => (
                <span
                  key={tag}
                  style={{
                    display: 'inline-block',
                    background: '#f0f0f0',
                    padding: '2px 6px',
                    borderRadius: '3px',
                    fontSize: '12px',
                    marginRight: '4px',
                    marginBottom: '4px',
                  }}
                >
                  {tag}
                </span>
              ))}
            </div>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
          <Text>没有找到匹配的模板</Text>
        </div>
      )}
    </div>
  );
};
