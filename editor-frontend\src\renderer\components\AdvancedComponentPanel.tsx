import React, { useState, useEffect } from 'react';
import { Card, Tabs, Collapse, Form, Input, Select, Switch, Slider, ColorPicker, Button, Space, Typography } from 'antd';
import {
  SettingOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  LayoutOutlined,
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;
const { Text } = Typography;

interface ComponentStyle {
  // 布局
  display?: string;
  position?: string;
  width?: string;
  height?: string;
  margin?: string;
  padding?: string;
  
  // 背景
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundSize?: string;
  backgroundPosition?: string;
  
  // 边框
  border?: string;
  borderRadius?: string;
  boxShadow?: string;
  
  // 文字
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
  color?: string;
  textAlign?: string;
  lineHeight?: string;
  
  // 其他
  opacity?: number;
  transform?: string;
  transition?: string;
}

interface AdvancedComponentPanelProps {
  selectedComponent?: any;
  onStyleChange?: (styles: ComponentStyle) => void;
  onAttributeChange?: (attributes: Record<string, any>) => void;
}

export const AdvancedComponentPanel: React.FC<AdvancedComponentPanelProps> = ({
  selectedComponent,
  onStyleChange,
  onAttributeChange,
}) => {
  const [form] = Form.useForm();
  const [currentStyles, setCurrentStyles] = useState<ComponentStyle>({});
  const [currentAttributes, setCurrentAttributes] = useState<Record<string, any>>({});

  useEffect(() => {
    if (selectedComponent) {
      // 从选中组件获取当前样式和属性
      const styles = selectedComponent.getStyle();
      const attributes = selectedComponent.getAttributes();
      
      setCurrentStyles(styles);
      setCurrentAttributes(attributes);
      
      // 更新表单值
      form.setFieldsValue({
        ...styles,
        ...attributes,
      });
    }
  }, [selectedComponent, form]);

  const handleStyleChange = (field: string, value: any) => {
    const newStyles = { ...currentStyles, [field]: value };
    setCurrentStyles(newStyles);
    
    if (onStyleChange) {
      onStyleChange(newStyles);
    }
  };

  const handleAttributeChange = (field: string, value: any) => {
    const newAttributes = { ...currentAttributes, [field]: value };
    setCurrentAttributes(newAttributes);
    
    if (onAttributeChange) {
      onAttributeChange(newAttributes);
    }
  };

  const presetStyles = {
    buttons: [
      { name: '主要按钮', styles: { backgroundColor: '#1890ff', color: 'white', border: 'none', padding: '8px 16px', borderRadius: '4px' } },
      { name: '次要按钮', styles: { backgroundColor: 'transparent', color: '#1890ff', border: '1px solid #1890ff', padding: '8px 16px', borderRadius: '4px' } },
      { name: '危险按钮', styles: { backgroundColor: '#ff4d4f', color: 'white', border: 'none', padding: '8px 16px', borderRadius: '4px' } },
    ],
    cards: [
      { name: '基础卡片', styles: { backgroundColor: 'white', border: '1px solid #e8e8e8', borderRadius: '8px', padding: '16px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' } },
      { name: '悬浮卡片', styles: { backgroundColor: 'white', border: 'none', borderRadius: '12px', padding: '24px', boxShadow: '0 4px 20px rgba(0,0,0,0.15)' } },
      { name: '简约卡片', styles: { backgroundColor: '#f8f9fa', border: 'none', borderRadius: '4px', padding: '20px' } },
    ],
    texts: [
      { name: '标题样式', styles: { fontSize: '24px', fontWeight: 'bold', color: '#333', marginBottom: '16px' } },
      { name: '副标题样式', styles: { fontSize: '18px', fontWeight: '500', color: '#666', marginBottom: '12px' } },
      { name: '正文样式', styles: { fontSize: '14px', lineHeight: '1.6', color: '#333' } },
    ],
  };

  if (!selectedComponent) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
          <SettingOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
          <div>请选择一个组件来编辑其属性</div>
        </div>
      </Card>
    );
  }

  return (
    <div style={{ height: '100%', overflow: 'auto' }}>
      <Tabs defaultActiveKey="styles" size="small">
        <TabPane tab={<><BgColorsOutlined /> 样式</>} key="styles">
          <Form form={form} layout="vertical" size="small">
            <Collapse size="small" ghost>
              {/* 布局设置 */}
              <Panel header={<><LayoutOutlined /> 布局</>} key="layout">
                <Form.Item label="显示方式">
                  <Select
                    value={currentStyles.display}
                    onChange={value => handleStyleChange('display', value)}
                    placeholder="选择显示方式"
                  >
                    <Option value="block">块级</Option>
                    <Option value="inline">行内</Option>
                    <Option value="inline-block">行内块</Option>
                    <Option value="flex">弹性布局</Option>
                    <Option value="grid">网格布局</Option>
                    <Option value="none">隐藏</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="定位方式">
                  <Select
                    value={currentStyles.position}
                    onChange={value => handleStyleChange('position', value)}
                    placeholder="选择定位方式"
                  >
                    <Option value="static">静态</Option>
                    <Option value="relative">相对</Option>
                    <Option value="absolute">绝对</Option>
                    <Option value="fixed">固定</Option>
                    <Option value="sticky">粘性</Option>
                  </Select>
                </Form.Item>

                <Space.Compact style={{ width: '100%' }}>
                  <Form.Item label="宽度" style={{ flex: 1, marginRight: '8px' }}>
                    <Input
                      value={currentStyles.width}
                      onChange={e => handleStyleChange('width', e.target.value)}
                      placeholder="auto"
                    />
                  </Form.Item>
                  <Form.Item label="高度" style={{ flex: 1 }}>
                    <Input
                      value={currentStyles.height}
                      onChange={e => handleStyleChange('height', e.target.value)}
                      placeholder="auto"
                    />
                  </Form.Item>
                </Space.Compact>

                <Space.Compact style={{ width: '100%' }}>
                  <Form.Item label="外边距" style={{ flex: 1, marginRight: '8px' }}>
                    <Input
                      value={currentStyles.margin}
                      onChange={e => handleStyleChange('margin', e.target.value)}
                      placeholder="0"
                    />
                  </Form.Item>
                  <Form.Item label="内边距" style={{ flex: 1 }}>
                    <Input
                      value={currentStyles.padding}
                      onChange={e => handleStyleChange('padding', e.target.value)}
                      placeholder="0"
                    />
                  </Form.Item>
                </Space.Compact>
              </Panel>

              {/* 背景设置 */}
              <Panel header={<><BgColorsOutlined /> 背景</>} key="background">
                <Form.Item label="背景颜色">
                  <ColorPicker
                    value={currentStyles.backgroundColor}
                    onChange={(_, hex) => handleStyleChange('backgroundColor', hex)}
                    showText
                  />
                </Form.Item>

                <Form.Item label="背景图片">
                  <Input
                    value={currentStyles.backgroundImage}
                    onChange={e => handleStyleChange('backgroundImage', e.target.value)}
                    placeholder="url(image.jpg)"
                  />
                </Form.Item>

                <Form.Item label="背景尺寸">
                  <Select
                    value={currentStyles.backgroundSize}
                    onChange={value => handleStyleChange('backgroundSize', value)}
                    placeholder="选择背景尺寸"
                  >
                    <Option value="auto">自动</Option>
                    <Option value="cover">覆盖</Option>
                    <Option value="contain">包含</Option>
                    <Option value="100% 100%">拉伸</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="背景位置">
                  <Select
                    value={currentStyles.backgroundPosition}
                    onChange={value => handleStyleChange('backgroundPosition', value)}
                    placeholder="选择背景位置"
                  >
                    <Option value="center">居中</Option>
                    <Option value="top">顶部</Option>
                    <Option value="bottom">底部</Option>
                    <Option value="left">左侧</Option>
                    <Option value="right">右侧</Option>
                  </Select>
                </Form.Item>
              </Panel>

              {/* 边框设置 */}
              <Panel header={<><BorderOutlined /> 边框</>} key="border">
                <Form.Item label="边框">
                  <Input
                    value={currentStyles.border}
                    onChange={e => handleStyleChange('border', e.target.value)}
                    placeholder="1px solid #ddd"
                  />
                </Form.Item>

                <Form.Item label="圆角">
                  <Input
                    value={currentStyles.borderRadius}
                    onChange={e => handleStyleChange('borderRadius', e.target.value)}
                    placeholder="0px"
                  />
                </Form.Item>

                <Form.Item label="阴影">
                  <Input
                    value={currentStyles.boxShadow}
                    onChange={e => handleStyleChange('boxShadow', e.target.value)}
                    placeholder="0 2px 8px rgba(0,0,0,0.1)"
                  />
                </Form.Item>
              </Panel>

              {/* 文字设置 */}
              <Panel header={<><FontSizeOutlined /> 文字</>} key="typography">
                <Form.Item label="字体大小">
                  <Input
                    value={currentStyles.fontSize}
                    onChange={e => handleStyleChange('fontSize', e.target.value)}
                    placeholder="14px"
                  />
                </Form.Item>

                <Form.Item label="字体粗细">
                  <Select
                    value={currentStyles.fontWeight}
                    onChange={value => handleStyleChange('fontWeight', value)}
                    placeholder="选择字体粗细"
                  >
                    <Option value="normal">正常</Option>
                    <Option value="bold">粗体</Option>
                    <Option value="lighter">细体</Option>
                    <Option value="100">100</Option>
                    <Option value="200">200</Option>
                    <Option value="300">300</Option>
                    <Option value="400">400</Option>
                    <Option value="500">500</Option>
                    <Option value="600">600</Option>
                    <Option value="700">700</Option>
                    <Option value="800">800</Option>
                    <Option value="900">900</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="字体族">
                  <Select
                    value={currentStyles.fontFamily}
                    onChange={value => handleStyleChange('fontFamily', value)}
                    placeholder="选择字体"
                  >
                    <Option value="inherit">继承</Option>
                    <Option value="Arial, sans-serif">Arial</Option>
                    <Option value="'Helvetica Neue', Helvetica, sans-serif">Helvetica</Option>
                    <Option value="'Times New Roman', Times, serif">Times New Roman</Option>
                    <Option value="Georgia, serif">Georgia</Option>
                    <Option value="'Courier New', Courier, monospace">Courier New</Option>
                    <Option value="'Microsoft YaHei', sans-serif">微软雅黑</Option>
                    <Option value="'SimSun', serif">宋体</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="文字颜色">
                  <ColorPicker
                    value={currentStyles.color}
                    onChange={(_, hex) => handleStyleChange('color', hex)}
                    showText
                  />
                </Form.Item>

                <Form.Item label="文字对齐">
                  <Select
                    value={currentStyles.textAlign}
                    onChange={value => handleStyleChange('textAlign', value)}
                    placeholder="选择对齐方式"
                  >
                    <Option value="left">左对齐</Option>
                    <Option value="center">居中</Option>
                    <Option value="right">右对齐</Option>
                    <Option value="justify">两端对齐</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="行高">
                  <Input
                    value={currentStyles.lineHeight}
                    onChange={e => handleStyleChange('lineHeight', e.target.value)}
                    placeholder="1.5"
                  />
                </Form.Item>
              </Panel>

              {/* 效果设置 */}
              <Panel header="效果" key="effects">
                <Form.Item label="透明度">
                  <Slider
                    min={0}
                    max={1}
                    step={0.1}
                    value={currentStyles.opacity || 1}
                    onChange={value => handleStyleChange('opacity', value)}
                  />
                </Form.Item>

                <Form.Item label="变换">
                  <Input
                    value={currentStyles.transform}
                    onChange={e => handleStyleChange('transform', e.target.value)}
                    placeholder="rotate(0deg) scale(1)"
                  />
                </Form.Item>

                <Form.Item label="过渡">
                  <Input
                    value={currentStyles.transition}
                    onChange={e => handleStyleChange('transition', e.target.value)}
                    placeholder="all 0.3s ease"
                  />
                </Form.Item>
              </Panel>
            </Collapse>
          </Form>
        </TabPane>

        <TabPane tab={<><SettingOutlined /> 属性</>} key="attributes">
          <Form form={form} layout="vertical" size="small">
            <Form.Item label="ID">
              <Input
                value={currentAttributes.id}
                onChange={e => handleAttributeChange('id', e.target.value)}
                placeholder="元素ID"
              />
            </Form.Item>

            <Form.Item label="Class">
              <Input
                value={currentAttributes.class}
                onChange={e => handleAttributeChange('class', e.target.value)}
                placeholder="CSS类名"
              />
            </Form.Item>

            <Form.Item label="标题">
              <Input
                value={currentAttributes.title}
                onChange={e => handleAttributeChange('title', e.target.value)}
                placeholder="鼠标悬停提示"
              />
            </Form.Item>

            {/* 根据组件类型显示特定属性 */}
            {selectedComponent?.get('tagName') === 'a' && (
              <>
                <Form.Item label="链接地址">
                  <Input
                    value={currentAttributes.href}
                    onChange={e => handleAttributeChange('href', e.target.value)}
                    placeholder="https://example.com"
                  />
                </Form.Item>
                <Form.Item label="打开方式">
                  <Select
                    value={currentAttributes.target}
                    onChange={value => handleAttributeChange('target', value)}
                    placeholder="选择打开方式"
                  >
                    <Option value="_self">当前窗口</Option>
                    <Option value="_blank">新窗口</Option>
                    <Option value="_parent">父窗口</Option>
                    <Option value="_top">顶层窗口</Option>
                  </Select>
                </Form.Item>
              </>
            )}

            {selectedComponent?.get('tagName') === 'img' && (
              <>
                <Form.Item label="图片地址">
                  <Input
                    value={currentAttributes.src}
                    onChange={e => handleAttributeChange('src', e.target.value)}
                    placeholder="图片URL"
                  />
                </Form.Item>
                <Form.Item label="替代文本">
                  <Input
                    value={currentAttributes.alt}
                    onChange={e => handleAttributeChange('alt', e.target.value)}
                    placeholder="图片描述"
                  />
                </Form.Item>
              </>
            )}
          </Form>
        </TabPane>

        <TabPane tab="预设" key="presets">
          <Collapse size="small" ghost>
            <Panel header="按钮样式" key="button-presets">
              <Space direction="vertical" style={{ width: '100%' }}>
                {presetStyles.buttons.map((preset, index) => (
                  <Button
                    key={index}
                    block
                    onClick={() => {
                      Object.entries(preset.styles).forEach(([key, value]) => {
                        handleStyleChange(key, value);
                      });
                    }}
                  >
                    {preset.name}
                  </Button>
                ))}
              </Space>
            </Panel>

            <Panel header="卡片样式" key="card-presets">
              <Space direction="vertical" style={{ width: '100%' }}>
                {presetStyles.cards.map((preset, index) => (
                  <Button
                    key={index}
                    block
                    onClick={() => {
                      Object.entries(preset.styles).forEach(([key, value]) => {
                        handleStyleChange(key, value);
                      });
                    }}
                  >
                    {preset.name}
                  </Button>
                ))}
              </Space>
            </Panel>

            <Panel header="文字样式" key="text-presets">
              <Space direction="vertical" style={{ width: '100%' }}>
                {presetStyles.texts.map((preset, index) => (
                  <Button
                    key={index}
                    block
                    onClick={() => {
                      Object.entries(preset.styles).forEach(([key, value]) => {
                        handleStyleChange(key, value);
                      });
                    }}
                  >
                    {preset.name}
                  </Button>
                ))}
              </Space>
            </Panel>
          </Collapse>
        </TabPane>
      </Tabs>
    </div>
  );
};
