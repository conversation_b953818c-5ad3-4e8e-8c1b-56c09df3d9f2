/**
 * GrapesJS 编辑器配置文件
 * 包含编辑器的基础配置、设备管理、存储管理等
 */

import type { EditorConfig } from 'grapesjs';

/**
 * 设备配置
 */
export const deviceConfig = {
  devices: [
    {
      name: '桌面',
      width: '',
      id: 'desktop',
    },
    {
      name: '平板',
      width: '768px',
      widthMedia: '992px',
      id: 'tablet',
    },
    {
      name: '手机',
      width: '320px',
      widthMedia: '768px',
      id: 'mobile',
    },
  ],
};

/**
 * 存储管理器配置
 */
export const storageConfig = {
  type: 'local',
  autosave: false,
  autoload: false,
  stepsBeforeSave: 1,
};

/**
 * 资源管理器配置
 */
export const assetManagerConfig = {
  embedAsBase64: true,
  upload: false,
  uploadText: '拖拽文件到这里或点击上传',
  addBtnText: '添加图片',
  modalTitle: '选择图片',
  multiUpload: true,
  showUrlInput: true,
  autoAdd: true,
};

/**
 * 面板配置
 */
export const panelsConfig = {
  defaults: [
    {
      id: 'layers',
      el: '.panel__right',
      resizable: {
        maxDim: 350,
        minDim: 200,
        tc: 0,
        cl: 1,
        cr: 0,
        bc: 0,
        keyWidth: 'flex-basis',
      },
    },
    {
      id: 'panel-switcher',
      el: '.panel__switcher',
      buttons: [
        {
          id: 'show-layers',
          active: true,
          label: '图层',
          command: 'show-layers',
          togglable: false,
        },
        {
          id: 'show-style',
          active: true,
          label: '样式',
          command: 'show-styles',
          togglable: false,
        },
        {
          id: 'show-traits',
          active: true,
          label: '属性',
          command: 'show-traits',
          togglable: false,
        },
      ],
    },
  ],
};

/**
 * 画布配置
 */
export const canvasConfig = {
  styles: [
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
  ],
  scripts: [],
};

/**
 * 基础编辑器配置
 */
export const baseEditorConfig: Partial<EditorConfig> = {
  // 基础配置
  height: '100vh',
  width: '100%',
  fromElement: false,
  showOffsets: true,
  noticeOnUnload: false,
  clearOnRender: false,
  
  // 模块配置
  storageManager: storageConfig,
  assetManager: assetManagerConfig,
  deviceManager: deviceConfig,
  panels: panelsConfig,
  canvas: canvasConfig,
  
  // 拖拽配置
  dragMode: 'absolute',
  
  // 选择器配置
  selectorManager: {
    appendTo: '.styles-container',
    escapeName: true,
  },
  
  // 样式管理器配置
  styleManager: {
    appendTo: '.styles-container',
    sectors: [
      {
        name: '尺寸',
        open: false,
        buildProps: ['width', 'min-height', 'padding'],
        properties: [
          {
            type: 'integer',
            name: '宽度',
            property: 'width',
            units: ['px', '%'],
            defaults: 'auto',
            min: 0,
          },
          {
            property: 'min-height',
            name: '最小高度',
            type: 'integer',
            units: ['px', '%'],
            defaults: 0,
            min: 0,
          },
          {
            property: 'padding',
            name: '内边距',
            type: 'composite',
            properties: [
              { name: '上', property: 'padding-top', type: 'integer', units: ['px', '%'], defaults: 0 },
              { name: '右', property: 'padding-right', type: 'integer', units: ['px', '%'], defaults: 0 },
              { name: '下', property: 'padding-bottom', type: 'integer', units: ['px', '%'], defaults: 0 },
              { name: '左', property: 'padding-left', type: 'integer', units: ['px', '%'], defaults: 0 },
            ],
          },
        ],
      },
      {
        name: '排版',
        open: true,
        buildProps: [
          'font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height',
          'text-align', 'text-decoration', 'text-shadow'
        ],
        properties: [
          {
            name: '字体',
            property: 'font-family',
            type: 'select',
            defaults: 'Inter, sans-serif',
            options: [
              { value: 'Inter, sans-serif', name: 'Inter' },
              { value: 'Arial, sans-serif', name: 'Arial' },
              { value: 'Georgia, serif', name: 'Georgia' },
              { value: '"Times New Roman", serif', name: 'Times New Roman' },
              { value: '"Courier New", monospace', name: 'Courier New' },
            ],
          },
          {
            name: '字号',
            property: 'font-size',
            type: 'integer',
            units: ['px', 'em', 'rem'],
            defaults: '16px',
            min: 8,
          },
          {
            name: '字重',
            property: 'font-weight',
            type: 'select',
            defaults: '400',
            options: [
              { value: '300', name: '细体' },
              { value: '400', name: '正常' },
              { value: '500', name: '中等' },
              { value: '600', name: '半粗' },
              { value: '700', name: '粗体' },
            ],
          },
          {
            name: '行高',
            property: 'line-height',
            type: 'integer',
            units: ['', 'px', 'em'],
            defaults: '1.6',
            min: 0,
            step: 0.1,
          },
          {
            name: '颜色',
            property: 'color',
            type: 'color',
            defaults: '#333333',
          },
          {
            name: '对齐',
            property: 'text-align',
            type: 'radio',
            defaults: 'left',
            options: [
              { id: 'left', name: '左对齐', className: 'fa fa-align-left' },
              { id: 'center', name: '居中', className: 'fa fa-align-center' },
              { id: 'right', name: '右对齐', className: 'fa fa-align-right' },
              { id: 'justify', name: '两端对齐', className: 'fa fa-align-justify' },
            ],
          },
        ],
      },
      {
        name: '装饰',
        open: false,
        buildProps: [
          'background-color', 'border-radius', 'border', 'box-shadow', 'background'
        ],
        properties: [
          {
            name: '背景色',
            property: 'background-color',
            type: 'color',
            defaults: 'transparent',
          },
          {
            name: '圆角',
            property: 'border-radius',
            type: 'integer',
            units: ['px', '%'],
            defaults: 0,
            min: 0,
          },
          {
            name: '边框',
            property: 'border',
            type: 'composite',
            properties: [
              { name: '宽度', property: 'border-width', type: 'integer', units: ['px'], defaults: 0 },
              { name: '样式', property: 'border-style', type: 'select', defaults: 'solid', options: [
                { value: 'none', name: '无' },
                { value: 'solid', name: '实线' },
                { value: 'dashed', name: '虚线' },
                { value: 'dotted', name: '点线' },
              ]},
              { name: '颜色', property: 'border-color', type: 'color', defaults: '#000000' },
            ],
          },
        ],
      },
      {
        name: '布局',
        open: false,
        buildProps: ['display', 'position', 'top', 'right', 'left', 'bottom'],
        properties: [
          {
            name: '显示',
            property: 'display',
            type: 'select',
            defaults: 'block',
            options: [
              { value: 'block', name: '块级' },
              { value: 'inline', name: '行内' },
              { value: 'inline-block', name: '行内块' },
              { value: 'flex', name: '弹性' },
              { value: 'none', name: '隐藏' },
            ],
          },
          {
            name: '定位',
            property: 'position',
            type: 'select',
            defaults: 'static',
            options: [
              { value: 'static', name: '静态' },
              { value: 'relative', name: '相对' },
              { value: 'absolute', name: '绝对' },
              { value: 'fixed', name: '固定' },
            ],
          },
        ],
      },
    ],
  },
  
  // 特征管理器配置
  traitManager: {
    appendTo: '.traits-container',
  },
  
  // 图层管理器配置
  layerManager: {
    appendTo: '.layers-container',
  },
  
  // 块管理器配置
  blockManager: {
    appendTo: '.blocks-container',
  },
};

/**
 * 获取完整的编辑器配置
 */
export const getEditorConfig = (customConfig: Partial<EditorConfig> = {}): Partial<EditorConfig> => {
  return {
    ...baseEditorConfig,
    ...customConfig,
  };
};
