appId: com.bookeditor.app
productName: 桌面端图书编辑器
copyright: Copyright © 2024 Book Editor Team

directories:
  output: out
  buildResources: assets

files:
  - dist/**/*
  - node_modules/**/*
  - package.json
  - "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}"
  - "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}"
  - "!**/node_modules/*.d.ts"
  - "!**/node_modules/.bin"
  - "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}"
  - "!.editorconfig"
  - "!**/._*"
  - "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}"
  - "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}"
  - "!**/{appveyor.yml,.travis.yml,circle.yml}"
  - "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"

extraResources:
  - from: assets
    to: assets
    filter:
      - "**/*"

win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
  icon: assets/icon.ico
  requestedExecutionLevel: asInvoker

nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  installerIcon: assets/icon.ico
  uninstallerIcon: assets/icon.ico
  installerHeaderIcon: assets/icon.ico
  createDesktopShortcut: always
  createStartMenuShortcut: true
  shortcutName: 桌面端图书编辑器

mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: assets/icon.icns
  category: public.app-category.productivity
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: assets/entitlements.mac.plist
  entitlementsInherit: assets/entitlements.mac.plist

dmg:
  title: 桌面端图书编辑器
  icon: assets/icon.icns
  background: assets/dmg-background.png
  contents:
    - x: 410
      y: 150
      type: link
      path: /Applications
    - x: 130
      y: 150
      type: file

linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
  icon: assets/icon.png
  category: Office
  synopsis: 桌面端图书编辑器
  description: 一个基于 Electron、React 和 Django 构建的现代化桌面图书编辑器应用

publish:
  provider: github
  owner: your-username
  repo: book-editor
  private: false

compression: maximum
removePackageScripts: true
nodeGypRebuild: false
buildDependenciesFromSource: false

afterSign: scripts/notarize.js
afterAllArtifactBuild: scripts/after-build.js
