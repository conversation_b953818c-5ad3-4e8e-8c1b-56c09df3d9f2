{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022", "DOM"], "outDir": "dist/main", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noFallthroughCasesInSwitch": true, "types": ["node", "electron"]}, "include": ["src/main/**/*", "src/shared/**/*"], "exclude": ["node_modules", "dist", "out", "src/renderer", "src/components", "src/pages"]}