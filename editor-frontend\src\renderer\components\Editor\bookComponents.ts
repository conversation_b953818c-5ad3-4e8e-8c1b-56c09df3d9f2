/**
 * 图书编辑专用组件定义
 * 包含标题、段落、图片、引用、章节分隔符等图书编辑常用组件
 */

import type { BlockProperties } from 'grapesjs';

/**
 * 图书组件分类
 */
export const BOOK_CATEGORIES = {
  LAYOUT: '布局',
  TEXT: '文本',
  MEDIA: '媒体',
  BOOK_ELEMENTS: '图书元素',
} as const;

/**
 * 标题组件（H1-H6）
 */
export const headingComponents: BlockProperties[] = [
  {
    id: 'book-heading-1',
    label: '主标题 (H1)',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-heading' },
    content: {
      type: 'text',
      tagName: 'h1',
      content: '章节标题',
      style: {
        'font-size': '2.5rem',
        'font-weight': '700',
        'line-height': '1.2',
        'margin': '2rem 0 1rem 0',
        'color': '#1a1a1a',
        'font-family': 'Inter, sans-serif',
      },
      traits: [
        {
          type: 'text',
          name: 'content',
          label: '标题内容',
        },
        {
          type: 'select',
          name: 'text-align',
          label: '对齐方式',
          options: [
            { value: 'left', name: '左对齐' },
            { value: 'center', name: '居中' },
            { value: 'right', name: '右对齐' },
          ],
        },
      ],
    },
  },
  {
    id: 'book-heading-2',
    label: '副标题 (H2)',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-heading' },
    content: {
      type: 'text',
      tagName: 'h2',
      content: '节标题',
      style: {
        'font-size': '2rem',
        'font-weight': '600',
        'line-height': '1.3',
        'margin': '1.5rem 0 0.75rem 0',
        'color': '#2a2a2a',
        'font-family': 'Inter, sans-serif',
      },
      traits: [
        {
          type: 'text',
          name: 'content',
          label: '标题内容',
        },
        {
          type: 'select',
          name: 'text-align',
          label: '对齐方式',
          options: [
            { value: 'left', name: '左对齐' },
            { value: 'center', name: '居中' },
            { value: 'right', name: '右对齐' },
          ],
        },
      ],
    },
  },
  {
    id: 'book-heading-3',
    label: '小标题 (H3)',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-heading' },
    content: {
      type: 'text',
      tagName: 'h3',
      content: '小节标题',
      style: {
        'font-size': '1.5rem',
        'font-weight': '600',
        'line-height': '1.4',
        'margin': '1.25rem 0 0.5rem 0',
        'color': '#3a3a3a',
        'font-family': 'Inter, sans-serif',
      },
      traits: [
        {
          type: 'text',
          name: 'content',
          label: '标题内容',
        },
        {
          type: 'select',
          name: 'text-align',
          label: '对齐方式',
          options: [
            { value: 'left', name: '左对齐' },
            { value: 'center', name: '居中' },
            { value: 'right', name: '右对齐' },
          ],
        },
      ],
    },
  },
];

/**
 * 段落组件
 */
export const paragraphComponents: BlockProperties[] = [
  {
    id: 'book-paragraph',
    label: '段落',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-paragraph' },
    content: {
      type: 'text',
      tagName: 'p',
      content: '这是一个段落。您可以在这里添加文本内容，支持富文本编辑。',
      style: {
        'font-size': '1rem',
        'line-height': '1.8',
        'margin': '0 0 1rem 0',
        'color': '#4a4a4a',
        'font-family': 'Inter, sans-serif',
        'text-align': 'justify',
      },
      traits: [
        {
          type: 'textarea',
          name: 'content',
          label: '段落内容',
        },
        {
          type: 'select',
          name: 'text-align',
          label: '对齐方式',
          options: [
            { value: 'left', name: '左对齐' },
            { value: 'center', name: '居中' },
            { value: 'right', name: '右对齐' },
            { value: 'justify', name: '两端对齐' },
          ],
        },
      ],
    },
  },
  {
    id: 'book-lead-paragraph',
    label: '引言段落',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-lead' },
    content: {
      type: 'text',
      tagName: 'p',
      content: '这是一个引言段落，通常用于章节开头或重要内容的引导。',
      style: {
        'font-size': '1.125rem',
        'line-height': '1.7',
        'margin': '0 0 1.5rem 0',
        'color': '#2a2a2a',
        'font-family': 'Inter, sans-serif',
        'font-weight': '500',
        'text-align': 'justify',
      },
      traits: [
        {
          type: 'textarea',
          name: 'content',
          label: '引言内容',
        },
      ],
    },
  },
];

/**
 * 引用组件
 */
export const quoteComponents: BlockProperties[] = [
  {
    id: 'book-blockquote',
    label: '块引用',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-quote' },
    content: {
      type: 'text',
      tagName: 'blockquote',
      content: '"这是一个引用文本示例，可以用来突出显示重要的观点或名言。"',
      style: {
        'border-left': '4px solid #1890ff',
        'padding': '1rem 1.5rem',
        'margin': '1.5rem 0',
        'background-color': '#f8f9fa',
        'font-style': 'italic',
        'font-size': '1.125rem',
        'line-height': '1.6',
        'color': '#555',
        'font-family': 'Inter, sans-serif',
        'border-radius': '0 4px 4px 0',
      },
      traits: [
        {
          type: 'textarea',
          name: 'content',
          label: '引用内容',
        },
        {
          type: 'text',
          name: 'cite',
          label: '引用来源',
        },
      ],
    },
  },
  {
    id: 'book-pullquote',
    label: '拉引用',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-pullquote' },
    content: {
      type: 'text',
      tagName: 'aside',
      content: '"这是一个拉引用，通常用于突出显示文章中的关键观点。"',
      style: {
        'border': '2px solid #1890ff',
        'padding': '2rem',
        'margin': '2rem auto',
        'text-align': 'center',
        'font-size': '1.25rem',
        'line-height': '1.5',
        'color': '#1890ff',
        'font-family': 'Inter, sans-serif',
        'font-weight': '500',
        'max-width': '80%',
        'border-radius': '8px',
        'background-color': '#f0f8ff',
      },
      traits: [
        {
          type: 'textarea',
          name: 'content',
          label: '拉引用内容',
        },
      ],
    },
  },
];

/**
 * 图片组件
 */
export const imageComponents: BlockProperties[] = [
  {
    id: 'book-image',
    label: '图片',
    category: BOOK_CATEGORIES.MEDIA,
    attributes: { class: 'gjs-block-image' },
    content: {
      type: 'image',
      style: {
        'max-width': '100%',
        'height': 'auto',
        'display': 'block',
        'margin': '1.5rem auto',
        'border-radius': '4px',
        'box-shadow': '0 2px 8px rgba(0,0,0,0.1)',
      },
      traits: [
        {
          type: 'text',
          name: 'src',
          label: '图片地址',
        },
        {
          type: 'text',
          name: 'alt',
          label: '替代文本',
        },
        {
          type: 'select',
          name: 'align',
          label: '对齐方式',
          options: [
            { value: 'left', name: '左对齐' },
            { value: 'center', name: '居中' },
            { value: 'right', name: '右对齐' },
          ],
        },
      ],
    },
  },
  {
    id: 'book-figure',
    label: '图表',
    category: BOOK_CATEGORIES.MEDIA,
    attributes: { class: 'gjs-block-figure' },
    content: `
      <figure style="margin: 2rem 0; text-align: center;">
        <img src="https://via.placeholder.com/600x400/f0f0f0/999?text=图片" 
             alt="图片描述" 
             style="max-width: 100%; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />
        <figcaption style="margin-top: 0.5rem; font-size: 0.875rem; color: #666; font-style: italic;">
          图片说明文字
        </figcaption>
      </figure>
    `,
  },
];

/**
 * 章节分隔符组件
 */
export const separatorComponents: BlockProperties[] = [
  {
    id: 'book-chapter-break',
    label: '章节分隔',
    category: BOOK_CATEGORIES.BOOK_ELEMENTS,
    attributes: { class: 'gjs-block-separator' },
    content: `
      <div style="text-align: center; margin: 3rem 0; padding: 2rem 0;">
        <div style="display: inline-block; font-size: 1.5rem; color: #ccc;">
          ❦ ❦ ❦
        </div>
      </div>
    `,
  },
  {
    id: 'book-section-break',
    label: '节分隔',
    category: BOOK_CATEGORIES.BOOK_ELEMENTS,
    attributes: { class: 'gjs-block-separator' },
    content: `
      <hr style="border: none; height: 1px; background: linear-gradient(to right, transparent, #ccc, transparent); margin: 2rem 0;" />
    `,
  },
  {
    id: 'book-page-break',
    label: '分页符',
    category: BOOK_CATEGORIES.BOOK_ELEMENTS,
    attributes: { class: 'gjs-block-page-break' },
    content: `
      <div style="page-break-before: always; margin: 0; padding: 0; height: 1px; border-top: 2px dashed #ddd;">
        <span style="background: white; padding: 0 1rem; color: #999; font-size: 0.75rem; position: relative; top: -0.5rem;">分页</span>
      </div>
    `,
  },
];

/**
 * 列表组件
 */
export const listComponents: BlockProperties[] = [
  {
    id: 'book-unordered-list',
    label: '无序列表',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-list' },
    content: `
      <ul style="padding-left: 1.5rem; margin: 1rem 0; line-height: 1.8; color: #4a4a4a; font-family: Inter, sans-serif;">
        <li style="margin-bottom: 0.5rem;">列表项 1</li>
        <li style="margin-bottom: 0.5rem;">列表项 2</li>
        <li style="margin-bottom: 0.5rem;">列表项 3</li>
      </ul>
    `,
  },
  {
    id: 'book-ordered-list',
    label: '有序列表',
    category: BOOK_CATEGORIES.TEXT,
    attributes: { class: 'gjs-block-list' },
    content: `
      <ol style="padding-left: 1.5rem; margin: 1rem 0; line-height: 1.8; color: #4a4a4a; font-family: Inter, sans-serif;">
        <li style="margin-bottom: 0.5rem;">第一项</li>
        <li style="margin-bottom: 0.5rem;">第二项</li>
        <li style="margin-bottom: 0.5rem;">第三项</li>
      </ol>
    `,
  },
];

/**
 * 所有图书组件
 */
export const allBookComponents: BlockProperties[] = [
  ...headingComponents,
  ...paragraphComponents,
  ...quoteComponents,
  ...imageComponents,
  ...separatorComponents,
  ...listComponents,
];

/**
 * 按分类组织的组件
 */
export const componentsByCategory = {
  [BOOK_CATEGORIES.TEXT]: [
    ...headingComponents,
    ...paragraphComponents,
    ...quoteComponents,
    ...listComponents,
  ],
  [BOOK_CATEGORIES.MEDIA]: imageComponents,
  [BOOK_CATEGORIES.BOOK_ELEMENTS]: separatorComponents,
};

/**
 * 获取所有组件分类
 */
export const getComponentCategories = () => Object.values(BOOK_CATEGORIES);

/**
 * 根据分类获取组件
 */
export const getComponentsByCategory = (category: string) => {
  return componentsByCategory[category as keyof typeof componentsByCategory] || [];
};
