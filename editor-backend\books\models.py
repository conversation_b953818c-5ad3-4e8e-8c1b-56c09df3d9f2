from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Book(models.Model):
    """图书模型"""
    title = models.CharField(max_length=200, verbose_name='标题')
    description = models.TextField(blank=True, verbose_name='描述')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='books', verbose_name='作者')
    cover = models.ImageField(upload_to='book_covers/', blank=True, null=True, verbose_name='封面')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_published = models.BooleanField(default=False, verbose_name='是否发布')

    class Meta:
        verbose_name = '图书'
        verbose_name_plural = '图书'
        ordering = ['-updated_at']

    def __str__(self):
        return self.title

class Chapter(models.Model):
    """章节模型"""
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='chapters', verbose_name='所属图书')
    title = models.CharField(max_length=200, verbose_name='章节标题')
    content = models.TextField(blank=True, verbose_name='章节内容')
    order = models.PositiveIntegerField(default=0, verbose_name='排序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '章节'
        verbose_name_plural = '章节'
        ordering = ['order', 'created_at']
        unique_together = ['book', 'order']

    def __str__(self):
        return f"{self.book.title} - {self.title}"
