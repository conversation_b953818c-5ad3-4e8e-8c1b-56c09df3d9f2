@echo off
echo 启动桌面端图书编辑器开发环境...

echo.
echo 1. 启动后端服务器...
start "Django Backend" cmd /k "cd /d %~dp0..\editor-backend && .\venv\Scripts\activate && python manage.py runserver"

echo.
echo 2. 等待后端启动...
timeout /t 3 /nobreak > nul

echo.
echo 3. 启动前端开发服务器...
start "Vite Frontend" cmd /k "cd /d %~dp0..\editor-frontend && npm run dev"

echo.
echo 4. 等待前端启动...
timeout /t 5 /nobreak > nul

echo.
echo 5. 启动 Electron 桌面应用...
start "Electron App" cmd /k "cd /d %~dp0..\editor-frontend && npm run electron:dev"

echo.
echo 开发环境启动完成！
echo 前端地址: http://localhost:5173
echo 后端地址: http://127.0.0.1:8000
echo API 文档: http://127.0.0.1:8000/api/docs/
echo.
pause
