# Application Configuration
VITE_APP_TITLE=桌面端图书编辑器
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=一个基于 Electron、React 和 Django 构建的现代化桌面图书编辑器应用

# API Configuration
VITE_API_BASE_URL=http://127.0.0.1:8000/api
VITE_API_TIMEOUT=10000

# Development Configuration
VITE_DEV_SERVER_PORT=5173
VITE_DEV_SERVER_HOST=localhost

# Feature Flags
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK_API=false
VITE_ENABLE_ANALYTICS=false

# Theme Configuration
VITE_DEFAULT_THEME=light
VITE_PRIMARY_COLOR=#1890ff

# Electron Configuration
VITE_ELECTRON_DEV_TOOLS=true
VITE_ELECTRON_SECURITY_WARNINGS=false
