import React, { useEffect, useState } from 'react';
import { Modal, Table, Typography, Tag, Button, Space } from 'antd';
import {
  KeyboardOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface KeyboardShortcutsProps {
  editor?: any;
  visible?: boolean;
  onClose?: () => void;
}

interface ShortcutItem {
  key: string;
  description: string;
  shortcut: string;
  category: string;
}

export const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
  editor,
  visible = false,
  onClose,
}) => {
  const [shortcuts] = useState<ShortcutItem[]>([
    // 基础操作
    { key: 'undo', description: '撤销', shortcut: 'Ctrl + Z', category: '基础操作' },
    { key: 'redo', description: '重做', shortcut: 'Ctrl + Shift + Z', category: '基础操作' },
    { key: 'save', description: '保存', shortcut: 'Ctrl + S', category: '基础操作' },
    { key: 'open', description: '打开', shortcut: 'Ctrl + O', category: '基础操作' },
    
    // 编辑操作
    { key: 'copy', description: '复制', shortcut: 'Ctrl + C', category: '编辑操作' },
    { key: 'cut', description: '剪切', shortcut: 'Ctrl + X', category: '编辑操作' },
    { key: 'paste', description: '粘贴', shortcut: 'Ctrl + V', category: '编辑操作' },
    { key: 'duplicate', description: '复制组件', shortcut: 'Ctrl + D', category: '编辑操作' },
    { key: 'delete', description: '删除', shortcut: 'Delete / Backspace', category: '编辑操作' },
    { key: 'select-all', description: '全选', shortcut: 'Ctrl + A', category: '编辑操作' },
    
    // 组件操作
    { key: 'move-up', description: '上移组件', shortcut: 'Ctrl + ↑', category: '组件操作' },
    { key: 'move-down', description: '下移组件', shortcut: 'Ctrl + ↓', category: '组件操作' },
    { key: 'toggle-visibility', description: '切换显示/隐藏', shortcut: 'Ctrl + Shift + H', category: '组件操作' },
    { key: 'group', description: '组合组件', shortcut: 'Ctrl + G', category: '组件操作' },
    { key: 'ungroup', description: '取消组合', shortcut: 'Ctrl + Shift + G', category: '组件操作' },
    
    // 视图操作
    { key: 'zoom-in', description: '放大', shortcut: 'Ctrl + +', category: '视图操作' },
    { key: 'zoom-out', description: '缩小', shortcut: 'Ctrl + -', category: '视图操作' },
    { key: 'zoom-fit', description: '适应窗口', shortcut: 'Ctrl + 0', category: '视图操作' },
    { key: 'fullscreen', description: '全屏模式', shortcut: 'F11', category: '视图操作' },
    { key: 'preview', description: '预览', shortcut: 'Ctrl + P', category: '视图操作' },
    
    // 面板操作
    { key: 'toggle-layers', description: '切换图层面板', shortcut: 'Ctrl + L', category: '面板操作' },
    { key: 'toggle-styles', description: '切换样式面板', shortcut: 'Ctrl + Shift + S', category: '面板操作' },
    { key: 'toggle-blocks', description: '切换组件面板', shortcut: 'Ctrl + B', category: '面板操作' },
    { key: 'toggle-code', description: '切换代码视图', shortcut: 'Ctrl + Shift + C', category: '面板操作' },
    
    // 设备切换
    { key: 'device-desktop', description: '桌面视图', shortcut: 'Ctrl + 1', category: '设备切换' },
    { key: 'device-tablet', description: '平板视图', shortcut: 'Ctrl + 2', category: '设备切换' },
    { key: 'device-mobile', description: '手机视图', shortcut: 'Ctrl + 3', category: '设备切换' },
  ]);

  useEffect(() => {
    if (!editor) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const { ctrlKey, shiftKey, altKey, key } = event;
      
      // 阻止某些默认行为
      if (ctrlKey) {
        switch (key.toLowerCase()) {
          case 's':
            event.preventDefault();
            // 触发保存
            break;
          case 'o':
            event.preventDefault();
            // 触发打开
            break;
          case 'p':
            if (!shiftKey) {
              event.preventDefault();
              // 触发预览
            }
            break;
          case 'd':
            event.preventDefault();
            editor.runCommand('duplicate');
            break;
          case 'g':
            event.preventDefault();
            if (shiftKey) {
              // 取消组合
            } else {
              // 组合
            }
            break;
          case 'l':
            event.preventDefault();
            // 切换图层面板
            break;
          case 'b':
            event.preventDefault();
            // 切换组件面板
            break;
          case 'h':
            if (shiftKey) {
              event.preventDefault();
              editor.runCommand('sw-visibility');
            }
            break;
          case '1':
            event.preventDefault();
            editor.runCommand('set-device-desktop');
            break;
          case '2':
            event.preventDefault();
            editor.runCommand('set-device-tablet');
            break;
          case '3':
            event.preventDefault();
            editor.runCommand('set-device-mobile');
            break;
          case 'arrowup':
            event.preventDefault();
            editor.runCommand('move-up');
            break;
          case 'arrowdown':
            event.preventDefault();
            editor.runCommand('move-down');
            break;
        }
      }
      
      // F11 全屏
      if (key === 'F11') {
        event.preventDefault();
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          document.documentElement.requestFullscreen();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [editor]);

  const columns = [
    {
      title: '功能',
      dataIndex: 'description',
      key: 'description',
      width: '40%',
    },
    {
      title: '快捷键',
      dataIndex: 'shortcut',
      key: 'shortcut',
      width: '30%',
      render: (shortcut: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace' }}>
          {shortcut}
        </Tag>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: '30%',
      render: (category: string) => (
        <Tag color="default">{category}</Tag>
      ),
    },
  ];

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, ShortcutItem[]>);

  return (
    <Modal
      title={
        <Space>
          <KeyboardOutlined />
          <span>键盘快捷键</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
      width={800}
      style={{ top: 20 }}
    >
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <InfoCircleOutlined style={{ color: '#1890ff' }} />
          <Text type="secondary">
            使用键盘快捷键可以大大提高编辑效率。以下是所有可用的快捷键：
          </Text>
        </Space>
      </div>

      {Object.entries(groupedShortcuts).map(([category, items]) => (
        <div key={category} style={{ marginBottom: '24px' }}>
          <Title level={5} style={{ marginBottom: '12px', color: '#1890ff' }}>
            {category}
          </Title>
          <Table
            dataSource={items}
            columns={columns}
            pagination={false}
            size="small"
            rowKey="key"
            style={{ marginBottom: '16px' }}
          />
        </div>
      ))}

      <div
        style={{
          background: '#f6f8fa',
          padding: '12px',
          borderRadius: '6px',
          marginTop: '16px',
        }}
      >
        <Title level={5} style={{ marginBottom: '8px' }}>
          使用提示
        </Title>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>大部分快捷键在编辑器获得焦点时生效</li>
          <li>组合键需要同时按下所有指定的键</li>
          <li>某些快捷键可能与浏览器快捷键冲突</li>
          <li>可以通过设置面板自定义快捷键</li>
        </ul>
      </div>
    </Modal>
  );
};

// 快捷键提示组件
export const ShortcutHint: React.FC<{
  shortcut: string;
  description: string;
  visible?: boolean;
}> = ({ shortcut, description, visible = false }) => {
  if (!visible) return null;

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
      }}
    >
      <KeyboardOutlined />
      <span>{description}</span>
      <Tag size="small" style={{ margin: 0, fontFamily: 'monospace' }}>
        {shortcut}
      </Tag>
    </div>
  );
};
