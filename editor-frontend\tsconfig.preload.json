{"extends": "./tsconfig.main.json", "compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "lib": ["ES2022", "DOM"], "outDir": "dist/main", "rootDir": "src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/main/preload.ts", "src/shared/**/*"], "exclude": ["node_modules", "dist", "src/renderer"]}