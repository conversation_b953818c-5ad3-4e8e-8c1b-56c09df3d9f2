# 桌面端图书编辑器开发环境配置

## 概述

本文档描述了桌面端图书编辑器项目的完整开发环境配置，基于 Vite + React + TypeScript + Electron 架构。

## 技术栈

### 前端技术栈
- **Electron 28.x** - 桌面应用框架
- **React 18.x** - 前端框架
- **TypeScript 5.x** - 类型系统
- **Vite** - 构建工具和开发服务器
- **Ant Design 5.x** - UI 组件库

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Husky** - Git hooks 管理
- **lint-staged** - 提交前代码检查

## 项目结构

```
editor-frontend/
├── src/
│   ├── main/                 # Electron 主进程
│   │   ├── main.ts          # 主进程入口
│   │   ├── preload.ts       # 预加载脚本
│   │   └── utils.ts         # 工具函数
│   ├── components/          # React 组件
│   ├── pages/              # 页面组件
│   ├── stores/             # 状态管理
│   ├── types/              # TypeScript 类型定义
│   ├── utils/              # 工具函数
│   ├── styles/             # 样式文件
│   └── assets/             # 静态资源
├── .vscode/                # VS Code 配置
├── .husky/                 # Git hooks
├── dist/                   # 构建输出
├── out/                    # Electron 打包输出
└── 配置文件
```

## 开发环境配置

### 1. TypeScript 配置

#### 严格模式配置
- 启用所有严格类型检查
- 配置路径映射（@/ 别名等）
- 分离主进程和渲染进程的 TypeScript 配置

#### 配置文件
- `tsconfig.json` - 根配置
- `tsconfig.app.json` - 渲染进程配置
- `tsconfig.main.json` - 主进程配置

### 2. ESLint 配置

#### 规则集
- React 专用规则
- TypeScript 严格规则
- Import 排序规则
- 代码质量规则

#### 特性
- 支持 React 18+ 新特性
- TypeScript 类型检查集成
- 自动修复功能
- 与 Prettier 集成

### 3. Prettier 配置

#### 格式化规则
- 单引号优先
- 分号结尾
- 100 字符行宽
- 2 空格缩进
- LF 行结束符

### 4. Vite 配置优化

#### 开发环境
- 热重载支持
- API 代理到后端
- 源码映射
- 快速刷新

#### 构建优化
- 代码分割
- 依赖优化
- 资源压缩
- 环境变量处理

### 5. VS Code 集成

#### 配置特性
- 自动格式化
- 智能提示
- 调试配置
- 任务配置
- 扩展推荐

#### 调试支持
- Electron 主进程调试
- React 渲染进程调试
- Django 后端调试
- 全栈调试配置

## 开发脚本

### 基础命令
```bash
npm run dev              # 启动开发服务器
npm run build            # 构建生产版本
npm run preview          # 预览构建结果
```

### 代码质量
```bash
npm run lint             # ESLint 检查
npm run lint:fix         # 自动修复 ESLint 问题
npm run format           # Prettier 格式化
npm run format:check     # 检查格式化
```

### 类型检查
```bash
npm run type-check       # 渲染进程类型检查
npm run type-check:main  # 主进程类型检查
npm run type-check:all   # 全部类型检查
```

### Electron 相关
```bash
npm run electron:dev     # 启动 Electron 开发版
npm run electron:build   # 构建 Electron 应用
npm run electron:pack    # 打包但不分发
```

### 测试和验证
```bash
npm run test:all         # 运行所有测试
npm run test:lint        # 代码规范测试
npm run test:format      # 格式化测试
npm run test:type        # 类型检查测试
```

## Git Hooks

### Pre-commit
- 运行 lint-staged 检查
- 执行类型检查
- 确保代码质量

### Commit-msg
- 验证提交信息格式
- 强制使用约定式提交

## 环境变量

### 开发环境变量
```env
VITE_APP_TITLE=桌面端图书编辑器
VITE_API_BASE_URL=http://127.0.0.1:8000/api
VITE_ENABLE_DEBUG=true
```

### 配置文件
- `.env.example` - 环境变量模板
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

## 验收标准

### ✅ 已完成的配置

1. **TypeScript 严格模式** - 所有类型检查通过
2. **ESLint 规则** - 代码质量检查配置完成
3. **Prettier 格式化** - 统一代码风格
4. **Vite 热重载** - 开发体验优化
5. **VS Code 集成** - 完整的 IDE 支持
6. **Git Hooks** - 自动化代码质量保证
7. **环境变量管理** - 开发和生产环境分离
8. **调试配置** - 主进程和渲染进程调试支持

### 🚀 开发体验特性

- **智能提示** - 完整的 TypeScript 类型支持
- **自动格式化** - 保存时自动格式化代码
- **错误检查** - 实时 ESLint 和 TypeScript 错误提示
- **快速重载** - Vite 热重载和 React Fast Refresh
- **一键调试** - VS Code 调试配置
- **提交检查** - Git hooks 自动代码质量检查

## 故障排除

### 常见问题

1. **TypeScript 错误** - 检查 tsconfig.json 配置
2. **ESLint 规则冲突** - 查看 eslint.config.js
3. **Prettier 格式化问题** - 检查 .prettierrc 配置
4. **Vite 构建失败** - 查看 vite.config.ts
5. **Electron 启动失败** - 检查主进程构建

### 调试技巧

1. 使用 VS Code 调试配置
2. 查看浏览器开发者工具
3. 检查 Electron 主进程日志
4. 使用 `npm run type-check:all` 检查类型
5. 使用 `npm run lint:fix` 自动修复问题

## 下一步

1. 添加单元测试配置
2. 集成 E2E 测试
3. 配置 CI/CD 流水线
4. 添加性能监控
5. 完善错误处理和日志记录
