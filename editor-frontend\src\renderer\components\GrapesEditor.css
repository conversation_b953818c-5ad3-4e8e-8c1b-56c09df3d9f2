/* GrapesJS 编辑器样式 */

.grapes-editor-container {
  width: 100%;
  height: 100%;
}

.grapes-editor-wrapper {
  position: relative;
  width: 100%;
  height: calc(100vh - 200px);
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.grapes-editor {
  width: 100%;
  height: 100%;
}

/* 自定义 GrapesJS 样式 */
.gjs-editor {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 工具栏样式 */
.gjs-pn-panel {
  background-color: #fafafa;
  border-color: #d9d9d9;
}

.gjs-pn-btn {
  color: rgba(0, 0, 0, 0.85);
  border-color: #d9d9d9;
  background-color: #ffffff;
  transition: all 0.3s;
}

.gjs-pn-btn:hover {
  color: #1890ff;
  border-color: #40a9ff;
  background-color: #f0f8ff;
}

.gjs-pn-active {
  color: #1890ff;
  border-color: #1890ff;
  background-color: #e6f7ff;
}

/* 块管理器样式 */
.gjs-blocks-c {
  background-color: #ffffff;
  border-color: #d9d9d9;
}

.gjs-block {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.85);
  transition: all 0.3s;
}

.gjs-block:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.gjs-block-label {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}

/* 样式管理器样式 */
.gjs-sm-sector {
  background-color: #ffffff;
  border-color: #d9d9d9;
}

.gjs-sm-title {
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.85);
  border-color: #d9d9d9;
  font-weight: 500;
}

.gjs-sm-property {
  border-color: #f0f0f0;
}

.gjs-sm-label {
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
}

.gjs-field {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.85);
  transition: all 0.3s;
}

.gjs-field:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}

/* 图层管理器样式 */
.gjs-lm-layer {
  background-color: #ffffff;
  border-color: #f0f0f0;
  color: rgba(0, 0, 0, 0.85);
}

.gjs-lm-layer:hover {
  background-color: #f5f5f5;
}

.gjs-lm-layer.gjs-lm-selected {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.gjs-lm-name {
  color: rgba(0, 0, 0, 0.85);
}

/* 特征管理器样式 */
.gjs-trt-trait {
  border-color: #f0f0f0;
}

.gjs-trt-label {
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
}

/* 画布样式 */
.gjs-cv-canvas {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
}

.gjs-frame {
  border: none;
}

/* 设备切换器样式 */
.gjs-pn-devices-c {
  background-color: #fafafa;
  border-color: #d9d9d9;
}

.gjs-pn-device {
  color: rgba(0, 0, 0, 0.65);
  border-color: #d9d9d9;
  background-color: #ffffff;
}

.gjs-pn-device:hover {
  color: #1890ff;
  border-color: #40a9ff;
}

.gjs-pn-device.gjs-pn-active {
  color: #1890ff;
  border-color: #1890ff;
  background-color: #e6f7ff;
}

/* 资源管理器样式 */
.gjs-am-assets {
  background-color: #ffffff;
  border-color: #d9d9d9;
}

.gjs-am-asset {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #ffffff;
  transition: all 0.3s;
}

.gjs-am-asset:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.gjs-am-asset-name {
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
}

/* 模态框样式 */
.gjs-mdl-dialog {
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gjs-mdl-header {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}

.gjs-mdl-content {
  background-color: #ffffff;
}

/* 代码编辑器样式 */
.gjs-cm-editor {
  background-color: #ffffff;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.CodeMirror {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.CodeMirror-cursor {
  border-left: 1px solid #1890ff;
}

.CodeMirror-selected {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grapes-editor-wrapper {
    height: calc(100vh - 150px);
  }
  
  .gjs-pn-panel {
    font-size: 12px;
  }
  
  .gjs-block {
    margin: 2px;
    padding: 4px;
  }
  
  .gjs-sm-property {
    padding: 4px;
  }
}

/* 自定义块样式 */
.gjs-block-section {
  background-color: #f0f8ff;
  border: 2px dashed #1890ff;
  color: #1890ff;
  text-align: center;
  padding: 20px;
  margin: 10px 0;
}

/* 选中状态样式 */
.gjs-selected {
  outline: 2px solid #1890ff !important;
  outline-offset: -2px;
}

.gjs-selected::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #1890ff;
  pointer-events: none;
  z-index: 1000;
}

/* 悬停状态样式 */
.gjs-hovered {
  outline: 1px dashed #40a9ff !important;
  outline-offset: -1px;
}

/* 拖拽状态样式 */
.gjs-dashed {
  border: 2px dashed #1890ff !important;
  background-color: rgba(24, 144, 255, 0.05) !important;
}

/* 工具提示样式 */
.gjs-tooltip {
  background-color: rgba(0, 0, 0, 0.85);
  color: #ffffff;
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 8px;
}

/* 滚动条样式 */
.gjs-blocks-c::-webkit-scrollbar,
.gjs-sm-sectors::-webkit-scrollbar,
.gjs-lm-layers::-webkit-scrollbar {
  width: 6px;
}

.gjs-blocks-c::-webkit-scrollbar-track,
.gjs-sm-sectors::-webkit-scrollbar-track,
.gjs-lm-layers::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.gjs-blocks-c::-webkit-scrollbar-thumb,
.gjs-sm-sectors::-webkit-scrollbar-thumb,
.gjs-lm-layers::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.gjs-blocks-c::-webkit-scrollbar-thumb:hover,
.gjs-sm-sectors::-webkit-scrollbar-thumb:hover,
.gjs-lm-layers::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
