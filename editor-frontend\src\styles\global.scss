@import './variables.scss';

// Reset and base styles
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px;
  line-height: 1.5715;
  color: $text-color;
  font-family: $font-family;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  background-color: $body-background;
  font-family: $font-family;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Scrollbar styles
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $gray-2;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: $gray-5;
  border-radius: 4px;

  &:hover {
    background: $gray-6;
  }
}

// Selection styles
::selection {
  background-color: rgba($primary-color, 0.2);
  color: $text-color;
}

::-moz-selection {
  background-color: rgba($primary-color, 0.2);
  color: $text-color;
}

// Focus styles
:focus {
  outline: 2px solid rgba($primary-color, 0.5);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

// Link styles
a {
  color: $primary-color;
  text-decoration: none;
  transition: color 0.3s;

  &:hover {
    color: lighten($primary-color, 10%);
  }

  &:active {
    color: darken($primary-color, 10%);
  }
}

// Button reset
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// Input reset
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  padding: $padding-xs $padding-sm;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

// Utility classes
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $text-color-secondary;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.bg-primary {
  background-color: $primary-color;
}

.bg-secondary {
  background-color: $gray-2;
}

.bg-white {
  background-color: $white;
}

.shadow-sm {
  box-shadow: $shadow-2;
}

.shadow-lg {
  box-shadow: $shadow-1-down;
}

.rounded {
  border-radius: $border-radius-base;
}

.rounded-sm {
  border-radius: $border-radius-sm;
}

.rounded-lg {
  border-radius: $border-radius-lg;
}

// Spacing utilities
@for $i from 0 through 10 {
  .m-#{$i} {
    margin: #{$i * 4}px;
  }

  .mt-#{$i} {
    margin-top: #{$i * 4}px;
  }

  .mr-#{$i} {
    margin-right: #{$i * 4}px;
  }

  .mb-#{$i} {
    margin-bottom: #{$i * 4}px;
  }

  .ml-#{$i} {
    margin-left: #{$i * 4}px;
  }

  .mx-#{$i} {
    margin-left: #{$i * 4}px;
    margin-right: #{$i * 4}px;
  }

  .my-#{$i} {
    margin-top: #{$i * 4}px;
    margin-bottom: #{$i * 4}px;
  }

  .p-#{$i} {
    padding: #{$i * 4}px;
  }

  .pt-#{$i} {
    padding-top: #{$i * 4}px;
  }

  .pr-#{$i} {
    padding-right: #{$i * 4}px;
  }

  .pb-#{$i} {
    padding-bottom: #{$i * 4}px;
  }

  .pl-#{$i} {
    padding-left: #{$i * 4}px;
  }

  .px-#{$i} {
    padding-left: #{$i * 4}px;
    padding-right: #{$i * 4}px;
  }

  .py-#{$i} {
    padding-top: #{$i * 4}px;
    padding-bottom: #{$i * 4}px;
  }
}

// Flexbox utilities
.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

// Animation classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

// Keyframes
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
