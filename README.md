# 桌面端图书编辑器

一个基于 Electron、React 和 Django 构建的现代化桌面图书编辑器应用。

## 技术栈

### 前端
- **Electron 28.x** - 桌面应用框架
- **React 18.x** - 前端框架
- **TypeScript 5.x** - 类型系统
- **Vite** - 构建工具
- **Ant Design 5.x** - UI 组件库

### 后端
- **Django 4.2.x** - Web 框架
- **Django REST Framework** - API 框架
- **Python 3.11** - 编程语言
- **PostgreSQL** - 数据库（生产环境）
- **SQLite** - 数据库（开发环境）

## 项目结构

```
htmlEditor/
├── editor-frontend/          # Electron + React 前端应用
├── editor-backend/           # Django REST API 后端
├── shared/                   # 共享类型定义和工具
├── docs/                     # 项目文档
├── scripts/                  # 构建和部署脚本
├── issues/                   # 任务和问题跟踪
└── rules/                    # 项目规范和文档
```

## 快速开始

### 环境要求

- Node.js 18.x+
- Python 3.11+
- Git

### 安装依赖

```bash
# 方法1：使用根目录脚本安装所有依赖
npm run install:all

# 方法2：分别安装
npm install                                    # 根目录依赖
cd editor-frontend && npm install             # 前端依赖
cd ../editor-backend && python -m venv venv   # 创建虚拟环境
cd editor-backend && .\venv\Scripts\activate && pip install -r requirements.txt  # 后端依赖
```

### 开发环境启动

#### 方法1：使用启动脚本（推荐）
```bash
# Windows
scripts\start-dev.bat

# 该脚本会自动启动：
# 1. Django 后端服务器 (http://127.0.0.1:8000)
# 2. Vite 前端开发服务器 (http://localhost:5173)
# 3. Electron 桌面应用
```

#### 方法2：分别启动
```bash
# 启动后端（终端1）
cd editor-backend
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # macOS/Linux
python manage.py runserver

# 启动前端（终端2）
cd editor-frontend
npm run dev

# 启动 Electron 桌面应用（终端3）
cd editor-frontend
npm run electron:dev
```

#### 方法3：使用根目录脚本
```bash
npm run dev              # 同时启动前后端
npm run electron:dev     # 启动 Electron 应用
```

### 构建

```bash
# 构建所有项目
npm run build

# 构建 Electron 应用
npm run electron:build
```

## 开发指南

### 代码规范

- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 使用 pre-commit hooks 确保代码质量

### 测试

```bash
# 运行所有测试
npm run test

# 运行前端测试
npm run test:frontend

# 运行后端测试
npm run test:backend
```

### 代码格式化

```bash
# 格式化代码
npm run format

# 检查代码规范
npm run lint
```

## 功能特性

- 🖥️ 跨平台桌面应用（Windows、macOS、Linux）
- 📝 所见即所得的图书编辑器
- 🎨 丰富的排版和样式选项
- 👥 实时协作编辑
- 📚 图书结构管理（章节、页面）
- 🔄 版本控制和历史记录
- 📤 多格式导出（PDF、EPUB、HTML）
- 🤖 AI 辅助内容生成
- 🎨 模板系统

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目链接：[https://github.com/your-username/book-editor](https://github.com/your-username/book-editor)
- 问题反馈：[https://github.com/your-username/book-editor/issues](https://github.com/your-username/book-editor/issues)

## 更新日志

### v1.0.0 (开发中)
- 初始项目架构搭建
- 基础前后端框架集成
- Electron 桌面应用框架
