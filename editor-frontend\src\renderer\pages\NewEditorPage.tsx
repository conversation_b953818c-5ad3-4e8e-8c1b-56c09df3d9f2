/**
 * 新编辑器页面
 * 使用基于 @grapesjs/react 的新编辑器组件
 */

import React, { useCallback } from 'react';
import { message } from 'antd';
import { ErrorBoundary } from 'react-error-boundary';

import { EditorComponent } from '../components/Editor';
import { ErrorFallback } from '../components/ErrorFallback';

/**
 * 新编辑器页面组件
 */
const NewEditorPage: React.FC = () => {
  /**
   * 处理保存
   */
  const handleSave = useCallback((content: { html: string; css: string }) => {
    console.log('保存内容:', content);
    message.success('内容已保存');
  }, []);

  /**
   * 处理加载
   */
  const handleLoad = useCallback(() => {
    console.log('编辑器加载完成');
  }, []);

  /**
   * 处理错误
   */
  const handleError = useCallback((error: Error) => {
    console.error('编辑器错误:', error);
    message.error(`编辑器错误: ${error.message}`);
  }, []);

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('编辑器页面错误:', error, errorInfo);
      }}
    >
      <div style={{ height: '100vh', overflow: 'hidden' }}>
        <EditorComponent
          height="100vh"
          onSave={handleSave}
          onLoad={handleLoad}
          onError={handleError}
          initialContent={{
            html: `
              <div style="padding: 40px; max-width: 800px; margin: 0 auto;">
                <h1 style="color: #1a1a1a; margin-bottom: 20px;">欢迎使用图书编辑器</h1>
                <p style="color: #4a4a4a; line-height: 1.8; margin-bottom: 20px;">
                  这是一个基于 GrapesJS React 的图书编辑器。您可以从左侧面板拖拽组件到这里，
                  然后在右侧面板中编辑组件的属性和样式。
                </p>
                <blockquote style="border-left: 4px solid #1890ff; padding: 16px 24px; margin: 24px 0; background: #f8f9fa; font-style: italic;">
                  "开始您的创作之旅，让文字和设计完美结合。"
                </blockquote>
              </div>
            `,
            css: `
              body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 0;
                background: #ffffff;
              }
            `,
          }}
        />
      </div>
    </ErrorBoundary>
  );
};

export default NewEditorPage;
