/**
 * 组件面板
 * 显示可拖拽的图书编辑组件
 */

import React, { useCallback, useMemo } from 'react';
import { Tabs, Empty, Tooltip } from 'antd';
import {
  FileTextOutlined,
  PictureOutlined,
  AppstoreOutlined,
  BookOutlined,
} from '@ant-design/icons';
import type { Editor } from 'grapesjs';

import {
  BOOK_CATEGORIES,
  componentsByCategory,
  getComponentCategories,
} from './bookComponents';

/**
 * 组件面板属性接口
 */
export interface ComponentPanelProps {
  /** 编辑器实例 */
  editor: Editor | null;
}

/**
 * 分类图标映射
 */
const CATEGORY_ICONS = {
  [BOOK_CATEGORIES.TEXT]: <FileTextOutlined />,
  [BOOK_CATEGORIES.MEDIA]: <PictureOutlined />,
  [BOOK_CATEGORIES.LAYOUT]: <AppstoreOutlined />,
  [BOOK_CATEGORIES.BOOK_ELEMENTS]: <BookOutlined />,
};

/**
 * 组件项组件
 */
interface ComponentItemProps {
  id: string;
  label: string;
  editor: Editor | null;
  onDragStart?: () => void;
  onDragEnd?: () => void;
}

const ComponentItem: React.FC<ComponentItemProps> = ({
  id,
  label,
  editor,
  onDragStart,
  onDragEnd,
}) => {
  /**
   * 处理组件点击
   */
  const handleClick = useCallback(() => {
    if (!editor) return;
    
    try {
      const blockManager = editor.BlockManager;
      const block = blockManager.get(id);
      
      if (block) {
        // 获取画布中心位置
        const canvas = editor.Canvas;
        const canvasEl = canvas.getElement();
        const rect = canvasEl?.getBoundingClientRect();
        
        if (rect) {
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;
          
          // 在画布中心添加组件
          const component = editor.addComponents(block.get('content'), {
            at: 'center',
          });
          
          // 选中新添加的组件
          if (component && component.length > 0) {
            editor.select(component[0]);
          }
        }
      }
    } catch (error) {
      console.error('添加组件失败:', error);
    }
  }, [editor, id]);

  /**
   * 处理拖拽开始
   */
  const handleDragStart = useCallback((event: React.DragEvent) => {
    if (!editor) return;
    
    event.dataTransfer.setData('text/block-id', id);
    onDragStart?.();
  }, [editor, id, onDragStart]);

  /**
   * 处理拖拽结束
   */
  const handleDragEnd = useCallback(() => {
    onDragEnd?.();
  }, [onDragEnd]);

  return (
    <Tooltip title={`点击或拖拽添加${label}`} placement="top">
      <div
        className="book-component-item"
        onClick={handleClick}
        draggable
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <span className="book-component-icon">
          {CATEGORY_ICONS[BOOK_CATEGORIES.TEXT]}
        </span>
        <span className="book-component-label">{label}</span>
      </div>
    </Tooltip>
  );
};

/**
 * 组件分类面板
 */
interface ComponentCategoryProps {
  category: string;
  editor: Editor | null;
}

const ComponentCategory: React.FC<ComponentCategoryProps> = ({
  category,
  editor,
}) => {
  const components = useMemo(() => {
    return componentsByCategory[category as keyof typeof componentsByCategory] || [];
  }, [category]);

  if (components.length === 0) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="暂无组件"
        style={{ margin: '20px 0' }}
      />
    );
  }

  return (
    <div className="book-components-panel">
      <div className="book-component-category">
        <div className="book-component-grid">
          {components.map((component) => (
            <ComponentItem
              key={component.id}
              id={component.id}
              label={component.label}
              editor={editor}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * 组件面板主组件
 */
const ComponentPanel: React.FC<ComponentPanelProps> = ({ editor }) => {
  const categories = useMemo(() => getComponentCategories(), []);

  /**
   * 生成标签页配置
   */
  const tabItems = useMemo(() => {
    return categories.map((category) => ({
      key: category,
      label: (
        <span>
          {CATEGORY_ICONS[category]}
          {category}
        </span>
      ),
      children: <ComponentCategory category={category} editor={editor} />,
    }));
  }, [categories, editor]);

  return (
    <div className="book-editor-sidebar-left">
      <Tabs
        defaultActiveKey={BOOK_CATEGORIES.TEXT}
        items={tabItems}
        size="small"
        tabPosition="top"
      />
    </div>
  );
};

export default React.memo(ComponentPanel);
