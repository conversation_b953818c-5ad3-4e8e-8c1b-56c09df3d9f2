/**
 * 属性面板组件
 * 显示选中组件的属性、样式和图层管理
 */

import React, { useEffect, useRef } from 'react';
import { Tabs, Empty } from 'antd';
import {
  SettingOutlined,
  BgColorsOutlined,
  ApartmentOutlined,
} from '@ant-design/icons';
import { useEditor, useEditorMaybe } from '@grapesjs/react';

/**
 * 特征管理器组件
 */
const TraitsManager: React.FC = () => {
  const editor = useEditor();
  const traitsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!editor || !traitsRef.current) return;

    const traitManager = editor.TraitManager;
    traitManager.render(traitsRef.current);

    return () => {
      // 清理
      if (traitsRef.current) {
        traitsRef.current.innerHTML = '';
      }
    };
  }, [editor]);

  return (
    <div className="book-properties-panel">
      <div ref={traitsRef} className="traits-container" />
    </div>
  );
};

/**
 * 样式管理器组件
 */
const StyleManager: React.FC = () => {
  const editor = useEditor();
  const stylesRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!editor || !stylesRef.current) return;

    const styleManager = editor.StyleManager;
    styleManager.render(stylesRef.current);

    return () => {
      // 清理
      if (stylesRef.current) {
        stylesRef.current.innerHTML = '';
      }
    };
  }, [editor]);

  return (
    <div className="book-properties-panel">
      <div ref={stylesRef} className="styles-container" />
    </div>
  );
};

/**
 * 图层管理器组件
 */
const LayerManager: React.FC = () => {
  const editor = useEditor();
  const layersRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!editor || !layersRef.current) return;

    const layerManager = editor.LayerManager;
    layerManager.render(layersRef.current);

    return () => {
      // 清理
      if (layersRef.current) {
        layersRef.current.innerHTML = '';
      }
    };
  }, [editor]);

  return (
    <div className="book-properties-panel">
      <div ref={layersRef} className="layers-container" />
    </div>
  );
};

/**
 * 属性面板主组件
 */
const PropertiesPanel: React.FC = () => {
  const editor = useEditorMaybe();

  // 如果编辑器未初始化，显示空状态
  if (!editor) {
    return (
      <div className="book-editor-sidebar-right">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="编辑器未初始化"
          style={{ margin: '50px 0' }}
        />
      </div>
    );
  }

  const tabItems = [
    {
      key: 'traits',
      label: (
        <span>
          <SettingOutlined />
          属性
        </span>
      ),
      children: <TraitsManager />,
    },
    {
      key: 'styles',
      label: (
        <span>
          <BgColorsOutlined />
          样式
        </span>
      ),
      children: <StyleManager />,
    },
    {
      key: 'layers',
      label: (
        <span>
          <ApartmentOutlined />
          图层
        </span>
      ),
      children: <LayerManager />,
    },
  ];

  return (
    <div className="book-editor-sidebar-right">
      <Tabs
        defaultActiveKey="traits"
        items={tabItems}
        size="small"
        tabPosition="top"
      />
    </div>
  );
};

export default React.memo(PropertiesPanel);
