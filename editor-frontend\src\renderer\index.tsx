import React from 'react';
import { createRoot } from 'react-dom/client';
import { ErrorBoundary } from 'react-error-boundary';

import App from './App';
import { ErrorFallback } from './components/ErrorFallback';

import '../styles/global.scss';

// 错误处理函数
const handleError = (error: Error, errorInfo: { componentStack: string }) => {
  console.error('React Error Boundary caught an error:', error, errorInfo);
  
  // 如果在 Electron 环境中，可以通过 IPC 发送错误信息到主进程
  if (window.electronAPI) {
    window.electronAPI.dialog.showError('应用错误', `${error.message}\n\n${error.stack}`);
  }
};

// 获取根元素
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

// 创建 React 根
const root = createRoot(container);

// 渲染应用
root.render(
  <React.StrictMode>
    <ErrorBoundary FallbackComponent={ErrorFallback} onError={handleError}>
      <App />
    </ErrorBoundary>
  </React.StrictMode>
);

// 开发环境下的热重载支持
if (import.meta.hot) {
  import.meta.hot.accept('./App', () => {
    // 重新导入并重新渲染应用
    import('./App').then(({ default: NextApp }) => {
      root.render(
        <React.StrictMode>
          <ErrorBoundary FallbackComponent={ErrorFallback} onError={handleError}>
            <NextApp />
          </ErrorBoundary>
        </React.StrictMode>
      );
    });
  });
}

// 全局错误处理
window.addEventListener('error', event => {
  console.error('Global error:', event.error);
  if (window.electronAPI) {
    window.electronAPI.dialog.showError('全局错误', event.error?.message || '未知错误');
  }
});

window.addEventListener('unhandledrejection', event => {
  console.error('Unhandled promise rejection:', event.reason);
  if (window.electronAPI) {
    window.electronAPI.dialog.showError(
      'Promise 错误',
      event.reason?.message || '未处理的 Promise 拒绝'
    );
  }
});

// 开发环境下显示性能信息
if (import.meta.env.DEV) {
  console.log('🚀 React 应用已启动');
  console.log('📦 环境变量:', import.meta.env);
  
  // 显示 Electron API 可用性
  if (window.electronAPI) {
    console.log('⚡ Electron API 可用');
    
    // 获取应用信息
    window.electronAPI.app.getInfo().then(info => {
      console.log('📱 应用信息:', info);
    });
  } else {
    console.warn('⚠️ Electron API 不可用，可能在浏览器环境中运行');
  }
}
