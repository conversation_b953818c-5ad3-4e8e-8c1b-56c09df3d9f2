import React, { useState, useEffect } from 'react';
import { <PERSON>ton, <PERSON>, Tooltip, Divider, Dropdown, message } from 'antd';
import {
  CopyOutlined,
  ScissorOutlined,
  SnippetsOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  <PERSON>UpOutlined,
  <PERSON>DownOutlined,
  LockOutlined,
  UnlockOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignBottomOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

interface EditingToolbarProps {
  selectedComponent?: any;
  editor?: any;
  onComponentChange?: () => void;
}

export const EditingToolbar: React.FC<EditingToolbarProps> = ({
  selectedComponent,
  editor,
  onComponentChange,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isLocked, setIsLocked] = useState(false);
  const [copiedComponent, setCopiedComponent] = useState<any>(null);

  useEffect(() => {
    if (selectedComponent) {
      const display = selectedComponent.getStyle('display');
      setIsVisible(display !== 'none');
      
      // 检查是否锁定（这里可以根据实际需求实现）
      const locked = selectedComponent.get('locked') || false;
      setIsLocked(locked);
    }
  }, [selectedComponent]);

  const handleCopy = () => {
    if (selectedComponent) {
      setCopiedComponent(selectedComponent);
      message.success('组件已复制');
    }
  };

  const handleCut = () => {
    if (selectedComponent) {
      setCopiedComponent(selectedComponent);
      selectedComponent.remove();
      message.success('组件已剪切');
      onComponentChange?.();
    }
  };

  const handlePaste = () => {
    if (copiedComponent && selectedComponent) {
      const cloned = copiedComponent.clone();
      const parent = selectedComponent.parent();
      parent.append(cloned);
      editor?.select(cloned);
      message.success('组件已粘贴');
      onComponentChange?.();
    }
  };

  const handleDuplicate = () => {
    if (selectedComponent && editor) {
      editor.runCommand('duplicate');
      message.success('组件已复制');
      onComponentChange?.();
    }
  };

  const handleDelete = () => {
    if (selectedComponent) {
      selectedComponent.remove();
      message.success('组件已删除');
      onComponentChange?.();
    }
  };

  const handleToggleVisibility = () => {
    if (selectedComponent) {
      const newDisplay = isVisible ? 'none' : 'block';
      selectedComponent.setStyle({ display: newDisplay });
      setIsVisible(!isVisible);
      message.success(isVisible ? '组件已隐藏' : '组件已显示');
      onComponentChange?.();
    }
  };

  const handleToggleLock = () => {
    if (selectedComponent) {
      const newLocked = !isLocked;
      selectedComponent.set('locked', newLocked);
      setIsLocked(newLocked);
      message.success(newLocked ? '组件已锁定' : '组件已解锁');
      onComponentChange?.();
    }
  };

  const handleMoveUp = () => {
    if (editor) {
      editor.runCommand('move-up');
      message.success('组件已上移');
      onComponentChange?.();
    }
  };

  const handleMoveDown = () => {
    if (editor) {
      editor.runCommand('move-down');
      message.success('组件已下移');
      onComponentChange?.();
    }
  };

  const handleAlign = (alignment: string) => {
    if (selectedComponent) {
      switch (alignment) {
        case 'left':
          selectedComponent.setStyle({ textAlign: 'left' });
          break;
        case 'center':
          selectedComponent.setStyle({ textAlign: 'center' });
          break;
        case 'right':
          selectedComponent.setStyle({ textAlign: 'right' });
          break;
        case 'top':
          selectedComponent.setStyle({ verticalAlign: 'top' });
          break;
        case 'middle':
          selectedComponent.setStyle({ verticalAlign: 'middle' });
          break;
        case 'bottom':
          selectedComponent.setStyle({ verticalAlign: 'bottom' });
          break;
      }
      message.success('对齐方式已应用');
      onComponentChange?.();
    }
  };

  const moreMenuItems: MenuProps['items'] = [
    {
      key: 'select-parent',
      label: '选择父元素',
      onClick: () => {
        if (selectedComponent && editor) {
          const parent = selectedComponent.parent();
          if (parent) {
            editor.select(parent);
          }
        }
      },
    },
    {
      key: 'select-children',
      label: '选择子元素',
      onClick: () => {
        if (selectedComponent && editor) {
          const children = selectedComponent.components();
          if (children.length > 0) {
            editor.select(children.at(0));
          }
        }
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'wrap-div',
      label: '用 DIV 包装',
      onClick: () => {
        if (selectedComponent) {
          const wrapper = selectedComponent.parent().append('<div></div>')[0];
          selectedComponent.remove();
          wrapper.append(selectedComponent);
          editor?.select(wrapper);
          message.success('已用 DIV 包装');
          onComponentChange?.();
        }
      },
    },
    {
      key: 'unwrap',
      label: '取消包装',
      onClick: () => {
        if (selectedComponent) {
          const parent = selectedComponent.parent();
          const grandParent = parent?.parent();
          if (parent && grandParent) {
            const children = selectedComponent.components();
            children.forEach((child: any) => {
              grandParent.append(child);
            });
            parent.remove();
            message.success('已取消包装');
            onComponentChange?.();
          }
        }
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'reset-styles',
      label: '重置样式',
      onClick: () => {
        if (selectedComponent) {
          selectedComponent.setStyle({});
          message.success('样式已重置');
          onComponentChange?.();
        }
      },
    },
  ];

  if (!selectedComponent) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: '80px',
        right: '20px',
        background: 'white',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        padding: '8px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        gap: '4px',
      }}
    >
      {/* 基础操作 */}
      <Space size="small">
        <Tooltip title="复制 (Ctrl+C)">
          <Button size="small" icon={<CopyOutlined />} onClick={handleCopy} />
        </Tooltip>
        <Tooltip title="剪切 (Ctrl+X)">
          <Button size="small" icon={<ScissorOutlined />} onClick={handleCut} />
        </Tooltip>
        <Tooltip title="粘贴 (Ctrl+V)">
          <Button
            size="small"
            icon={<SnippetsOutlined />}
            onClick={handlePaste}
            disabled={!copiedComponent}
          />
        </Tooltip>
        <Tooltip title="复制 (Ctrl+D)">
          <Button size="small" icon={<CopyOutlined />} onClick={handleDuplicate} />
        </Tooltip>
        <Tooltip title="删除 (Delete)">
          <Button size="small" danger icon={<DeleteOutlined />} onClick={handleDelete} />
        </Tooltip>
      </Space>

      <Divider style={{ margin: '4px 0' }} />

      {/* 显示和锁定 */}
      <Space size="small">
        <Tooltip title={isVisible ? '隐藏' : '显示'}>
          <Button
            size="small"
            icon={isVisible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            onClick={handleToggleVisibility}
          />
        </Tooltip>
        <Tooltip title={isLocked ? '解锁' : '锁定'}>
          <Button
            size="small"
            icon={isLocked ? <LockOutlined /> : <UnlockOutlined />}
            onClick={handleToggleLock}
          />
        </Tooltip>
      </Space>

      <Divider style={{ margin: '4px 0' }} />

      {/* 移动操作 */}
      <Space size="small">
        <Tooltip title="上移 (Ctrl+↑)">
          <Button size="small" icon={<ArrowUpOutlined />} onClick={handleMoveUp} />
        </Tooltip>
        <Tooltip title="下移 (Ctrl+↓)">
          <Button size="small" icon={<ArrowDownOutlined />} onClick={handleMoveDown} />
        </Tooltip>
      </Space>

      <Divider style={{ margin: '4px 0' }} />

      {/* 对齐操作 */}
      <Space size="small">
        <Tooltip title="左对齐">
          <Button
            size="small"
            icon={<AlignLeftOutlined />}
            onClick={() => handleAlign('left')}
          />
        </Tooltip>
        <Tooltip title="居中对齐">
          <Button
            size="small"
            icon={<AlignCenterOutlined />}
            onClick={() => handleAlign('center')}
          />
        </Tooltip>
        <Tooltip title="右对齐">
          <Button
            size="small"
            icon={<AlignRightOutlined />}
            onClick={() => handleAlign('right')}
          />
        </Tooltip>
      </Space>

      <Space size="small">
        <Tooltip title="顶部对齐">
          <Button
            size="small"
            icon={<VerticalAlignTopOutlined />}
            onClick={() => handleAlign('top')}
          />
        </Tooltip>
        <Tooltip title="垂直居中">
          <Button
            size="small"
            icon={<VerticalAlignMiddleOutlined />}
            onClick={() => handleAlign('middle')}
          />
        </Tooltip>
        <Tooltip title="底部对齐">
          <Button
            size="small"
            icon={<VerticalAlignBottomOutlined />}
            onClick={() => handleAlign('bottom')}
          />
        </Tooltip>
      </Space>

      <Divider style={{ margin: '4px 0' }} />

      {/* 更多操作 */}
      <Dropdown menu={{ items: moreMenuItems }} placement="bottomRight">
        <Button size="small" icon={<MoreOutlined />} />
      </Dropdown>
    </div>
  );
};
