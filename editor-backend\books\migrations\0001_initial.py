# Generated by Django 4.2.16 on 2025-08-21 08:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('cover', models.ImageField(blank=True, null=True, upload_to='book_covers/', verbose_name='封面')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_published', models.BooleanField(default=False, verbose_name='是否发布')),
            ],
            options={
                'verbose_name': '图书',
                'verbose_name_plural': '图书',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Chapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='章节标题')),
                ('content', models.TextField(blank=True, verbose_name='章节内容')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chapters', to='books.book', verbose_name='所属图书')),
            ],
            options={
                'verbose_name': '章节',
                'verbose_name_plural': '章节',
                'ordering': ['order', 'created_at'],
            },
        ),
    ]
