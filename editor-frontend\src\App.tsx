import { ConfigProvider, Layout, Typography } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import React from 'react';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <Layout style={{ minHeight: '100vh' }}>
        <Header
          style={{
            display: 'flex',
            alignItems: 'center',
            background: '#001529',
          }}
        >
          <Title level={3} style={{ color: 'white', margin: 0 }}>
            桌面端图书编辑器
          </Title>
        </Header>

        <Content style={{ padding: '24px' }}>
          <div
            style={{
              background: '#fff',
              padding: '24px',
              borderRadius: '8px',
              textAlign: 'center',
            }}
          >
            <Title level={2}>欢迎使用图书编辑器</Title>
            <p>这是一个基于 Electron、React 和 Django 构建的现代化桌面图书编辑器应用。</p>
            <p>前端技术栈：Electron 28.x + React 18.x + TypeScript 5.x + Vite + Ant Design</p>
            <p>后端技术栈：Django 4.2.x + Django REST Framework + Python 3.11</p>
          </div>
        </Content>

        <Footer style={{ textAlign: 'center' }}>
          桌面端图书编辑器 ©2024 Created by Book Editor Team
        </Footer>
      </Layout>
    </ConfigProvider>
  );
}

export default App;
