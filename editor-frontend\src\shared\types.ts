// Electron IPC 通信类型定义

// 主进程到渲染进程的消息类型
export interface MainToRendererMessages {
  'menu:new-book': () => void;
  'menu:open-book': (filePath: string) => void;
  'menu:save-book': () => void;
  'app:update-available': (version: string) => void;
  'app:update-downloaded': () => void;
  'window:focus': () => void;
  'window:blur': () => void;
  'window:maximize': () => void;
  'window:unmaximize': () => void;
  'window:minimize': () => void;
  'window:restore': () => void;
}

// 渲染进程到主进程的消息类型
export interface RendererToMainMessages {
  'app:get-version': () => Promise<string>;
  'app:get-path': (name: string) => Promise<string>;
  'app:quit': () => void;
  'app:restart': () => void;
  'window:minimize': () => void;
  'window:maximize': () => void;
  'window:unmaximize': () => void;
  'window:close': () => void;
  'window:set-title': (title: string) => void;
  'window:set-size': (width: number, height: number) => void;
  'window:center': () => void;
  'dialog:show-message': (options: Electron.MessageBoxOptions) => Promise<Electron.MessageBoxReturnValue>;
  'dialog:show-error': (title: string, content: string) => void;
  'dialog:show-open': (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>;
  'dialog:show-save': (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>;
  'file:read': (filePath: string) => Promise<string>;
  'file:write': (filePath: string, content: string) => Promise<void>;
  'file:exists': (filePath: string) => Promise<boolean>;
  'file:delete': (filePath: string) => Promise<void>;
  'file:copy': (src: string, dest: string) => Promise<void>;
  'file:move': (src: string, dest: string) => Promise<void>;
  'folder:create': (folderPath: string) => Promise<void>;
  'folder:read': (folderPath: string) => Promise<string[]>;
  'shell:open-external': (url: string) => Promise<void>;
  'shell:show-item-in-folder': (fullPath: string) => void;
  'shell:open-path': (path: string) => Promise<string>;
}

// 窗口状态类型
export interface WindowState {
  isMaximized: boolean;
  isMinimized: boolean;
  isFullscreen: boolean;
  isFocused: boolean;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// 应用信息类型
export interface AppInfo {
  name: string;
  version: string;
  platform: string;
  arch: string;
  electronVersion: string;
  chromeVersion: string;
  nodeVersion: string;
}

// 文件操作结果类型
export interface FileOperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

// 菜单项类型
export interface MenuItemConfig {
  id: string;
  label: string;
  accelerator?: string;
  role?: Electron.MenuItemConstructorOptions['role'];
  type?: 'normal' | 'separator' | 'submenu' | 'checkbox' | 'radio';
  checked?: boolean;
  enabled?: boolean;
  visible?: boolean;
  submenu?: MenuItemConfig[];
  click?: () => void;
}

// 通知类型
export interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  urgency?: 'normal' | 'critical' | 'low';
  actions?: Array<{
    type: 'button';
    text: string;
  }>;
}

// 主题配置类型
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
}

// 应用设置类型
export interface AppSettings {
  theme: ThemeConfig;
  window: {
    width: number;
    height: number;
    x?: number;
    y?: number;
    maximized: boolean;
    fullscreen: boolean;
  };
  editor: {
    fontSize: number;
    fontFamily: string;
    lineHeight: number;
    tabSize: number;
    wordWrap: boolean;
    showLineNumbers: boolean;
    autoSave: boolean;
    autoSaveDelay: number;
  };
  general: {
    language: string;
    autoUpdate: boolean;
    startupBehavior: 'new' | 'restore' | 'empty';
    confirmBeforeQuit: boolean;
  };
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  stack?: string;
  timestamp: Date;
  context?: Record<string, any>;
}

// 进度信息类型
export interface ProgressInfo {
  percent: number;
  transferred: number;
  total: number;
  bytesPerSecond: number;
}

// 更新信息类型
export interface UpdateInfo {
  version: string;
  releaseDate: string;
  releaseNotes: string;
  downloadUrl: string;
  size: number;
}

// IPC 事件类型
export type IPCEventMap = MainToRendererMessages & RendererToMainMessages;

// 类型工具
export type IPCEventNames = keyof IPCEventMap;
export type IPCEventHandler<T extends IPCEventNames> = IPCEventMap[T];

// 安全上下文类型
export interface SecurityContext {
  nodeIntegration: boolean;
  contextIsolation: boolean;
  webSecurity: boolean;
  allowRunningInsecureContent: boolean;
  experimentalFeatures: boolean;
}

// 开发工具类型
export interface DevToolsConfig {
  enabled: boolean;
  detached: boolean;
  mode: 'right' | 'bottom' | 'undocked' | 'detach';
}

// 性能监控类型
export interface PerformanceMetrics {
  memoryUsage: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  cpuUsage: {
    percentCPUUsage: number;
    idleWakeupsPerSecond: number;
  };
  timestamp: number;
}
