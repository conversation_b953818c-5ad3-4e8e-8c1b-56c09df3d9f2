import { useEffect, useState } from 'react';
import type { ElectronAPI } from '../../main/preload';

/**
 * 用于访问 Electron API 的 Hook
 * 提供类型安全的 Electron API 访问
 */
export const useElectronAPI = (): ElectronAPI | null => {
  const [electronAPI, setElectronAPI] = useState<ElectronAPI | null>(null);

  useEffect(() => {
    // 检查是否在 Electron 环境中
    if (typeof window !== 'undefined' && window.electronAPI) {
      setElectronAPI(window.electronAPI);
    } else {
      console.warn('Electron API not available. Running in browser mode?');
    }
  }, []);

  return electronAPI;
};

/**
 * 用于监听窗口事件的 Hook
 */
export const useWindowEvents = () => {
  const electronAPI = useElectronAPI();

  useEffect(() => {
    if (!electronAPI) return;

    const handleFocus = () => {
      console.log('Window focused');
    };

    const handleBlur = () => {
      console.log('Window blurred');
    };

    const handleMaximize = () => {
      console.log('Window maximized');
    };

    const handleUnmaximize = () => {
      console.log('Window unmaximized');
    };

    const handleMinimize = () => {
      console.log('Window minimized');
    };

    const handleRestore = () => {
      console.log('Window restored');
    };

    // 注册事件监听器
    electronAPI.window.onFocus(handleFocus);
    electronAPI.window.onBlur(handleBlur);
    electronAPI.window.onMaximize(handleMaximize);
    electronAPI.window.onUnmaximize(handleUnmaximize);
    electronAPI.window.onMinimize(handleMinimize);
    electronAPI.window.onRestore(handleRestore);

    // 清理函数
    return () => {
      // 注意：这里需要实现取消监听的逻辑
      // 在实际的 Electron API 中，你可能需要提供 removeListener 方法
    };
  }, [electronAPI]);
};

/**
 * 用于监听菜单事件的 Hook
 */
export const useMenuEvents = (callbacks: {
  onNewBook?: () => void;
  onOpenBook?: (filePath: string) => void;
  onSaveBook?: () => void;
}) => {
  const electronAPI = useElectronAPI();

  useEffect(() => {
    if (!electronAPI) return;

    if (callbacks.onNewBook) {
      electronAPI.menu.onNewBook(callbacks.onNewBook);
    }

    if (callbacks.onOpenBook) {
      electronAPI.menu.onOpenBook(callbacks.onOpenBook);
    }

    if (callbacks.onSaveBook) {
      electronAPI.menu.onSaveBook(callbacks.onSaveBook);
    }
  }, [electronAPI, callbacks]);
};

/**
 * 用于获取应用信息的 Hook
 */
export const useAppInfo = () => {
  const [appInfo, setAppInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const electronAPI = useElectronAPI();

  useEffect(() => {
    const fetchAppInfo = async () => {
      if (!electronAPI) {
        setError('Electron API not available');
        setLoading(false);
        return;
      }

      try {
        const info = await electronAPI.app.getInfo();
        setAppInfo(info);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchAppInfo();
  }, [electronAPI]);

  return { appInfo, loading, error };
};

/**
 * 用于文件操作的 Hook
 */
export const useFileOperations = () => {
  const electronAPI = useElectronAPI();

  const readFile = async (filePath: string): Promise<string> => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    return await electronAPI.file.read(filePath);
  };

  const writeFile = async (filePath: string, content: string): Promise<void> => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    await electronAPI.file.write(filePath, content);
  };

  const fileExists = async (filePath: string): Promise<boolean> => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    return await electronAPI.file.exists(filePath);
  };

  const deleteFile = async (filePath: string): Promise<void> => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    await electronAPI.file.delete(filePath);
  };

  const copyFile = async (src: string, dest: string): Promise<void> => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    await electronAPI.file.copy(src, dest);
  };

  const moveFile = async (src: string, dest: string): Promise<void> => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    await electronAPI.file.move(src, dest);
  };

  return {
    readFile,
    writeFile,
    fileExists,
    deleteFile,
    copyFile,
    moveFile,
  };
};

/**
 * 用于对话框操作的 Hook
 */
export const useDialogs = () => {
  const electronAPI = useElectronAPI();

  const showMessage = async (options: Electron.MessageBoxOptions) => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    return await electronAPI.dialog.showMessage(options);
  };

  const showError = (title: string, content: string) => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    electronAPI.dialog.showError(title, content);
  };

  const showOpenDialog = async (options: Electron.OpenDialogOptions) => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    return await electronAPI.dialog.showOpen(options);
  };

  const showSaveDialog = async (options: Electron.SaveDialogOptions) => {
    if (!electronAPI) {
      throw new Error('Electron API not available');
    }
    return await electronAPI.dialog.showSave(options);
  };

  return {
    showMessage,
    showError,
    showOpenDialog,
    showSaveDialog,
  };
};
