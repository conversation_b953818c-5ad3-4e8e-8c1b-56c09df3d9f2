// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  avatar?: string;
  bio?: string;
  createdAt: string;
  updatedAt: string;
}

// 图书相关类型
export interface Book {
  id: number;
  title: string;
  description?: string;
  author: User;
  cover?: string;
  createdAt: string;
  updatedAt: string;
  isPublished: boolean;
  chapters: Chapter[];
}

// 章节相关类型
export interface Chapter {
  id: number;
  bookId: number;
  title: string;
  content: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: {
    results: T[];
    count: number;
    next?: string;
    previous?: string;
  };
  message?: string;
}

// 认证相关类型
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

// 应用状态类型
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface BookState {
  books: Book[];
  currentBook: Book | null;
  isLoading: boolean;
  error: string | null;
}

// 编辑器相关类型
export interface EditorState {
  content: string;
  isDirty: boolean;
  isAutoSaving: boolean;
  lastSaved?: Date;
}

// 主题相关类型
export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  borderRadius: number;
}

// 设置相关类型
export interface AppSettings {
  theme: ThemeConfig;
  editor: {
    fontSize: number;
    fontFamily: string;
    lineHeight: number;
    wordWrap: boolean;
    autoSave: boolean;
    autoSaveInterval: number;
  };
  general: {
    language: string;
    autoUpdate: boolean;
    showLineNumbers: boolean;
  };
}

// 文件相关类型
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  type: string;
  lastModified: Date;
}

// 导出相关类型
export type ExportFormat = 'pdf' | 'epub' | 'html' | 'docx' | 'txt';

export interface ExportOptions {
  format: ExportFormat;
  includeImages: boolean;
  includeToc: boolean;
  pageSize?: 'A4' | 'A5' | 'Letter';
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// 通知类型
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

// 快捷键类型
export interface Shortcut {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  meta?: boolean;
  action: string;
  description: string;
}

// 插件相关类型
export interface Plugin {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  enabled: boolean;
  config?: Record<string, any>;
}

// 窗口状态类型
export interface WindowState {
  isMaximized: boolean;
  isMinimized: boolean;
  isFullscreen: boolean;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}
