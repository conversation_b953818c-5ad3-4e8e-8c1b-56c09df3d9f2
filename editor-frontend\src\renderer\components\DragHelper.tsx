import React, { useEffect, useState } from 'react';
import { Typography, Tag } from 'antd';
import {
  DragOutlined,
  AimOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';

const { Text } = Typography;

interface DragHelperProps {
  editor?: any;
  isVisible?: boolean;
}

interface DragInfo {
  isDragging: boolean;
  draggedComponent?: any;
  targetComponent?: any;
  position?: { x: number; y: number };
  canDrop?: boolean;
}

export const DragHelper: React.FC<DragHelperProps> = ({ editor, isVisible = true }) => {
  const [dragInfo, setDragInfo] = useState<DragInfo>({
    isDragging: false,
  });

  useEffect(() => {
    if (!editor) return;

    const handleDragStart = (component: any) => {
      setDragInfo({
        isDragging: true,
        draggedComponent: component,
        canDrop: false,
      });
    };

    const handleDragMove = (event: any) => {
      if (dragInfo.isDragging) {
        setDragInfo(prev => ({
          ...prev,
          position: { x: event.clientX, y: event.clientY },
        }));
      }
    };

    const handleDragOver = (component: any) => {
      setDragInfo(prev => ({
        ...prev,
        targetComponent: component,
        canDrop: component && component !== prev.draggedComponent,
      }));
    };

    const handleDragEnd = () => {
      setDragInfo({
        isDragging: false,
      });
    };

    // 监听拖拽事件
    editor.on('block:drag:start', handleDragStart);
    editor.on('component:drag:start', handleDragStart);
    editor.on('canvas:dragmove', handleDragMove);
    editor.on('component:drag:over', handleDragOver);
    editor.on('block:drag:stop', handleDragEnd);
    editor.on('component:drag:end', handleDragEnd);

    return () => {
      editor.off('block:drag:start', handleDragStart);
      editor.off('component:drag:start', handleDragStart);
      editor.off('canvas:dragmove', handleDragMove);
      editor.off('component:drag:over', handleDragOver);
      editor.off('block:drag:stop', handleDragEnd);
      editor.off('component:drag:end', handleDragEnd);
    };
  }, [editor, dragInfo.isDragging]);

  if (!isVisible || !dragInfo.isDragging) {
    return null;
  }

  return (
    <>
      {/* 拖拽指示器 */}
      {dragInfo.position && (
        <div
          style={{
            position: 'fixed',
            left: dragInfo.position.x + 10,
            top: dragInfo.position.y - 30,
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: 10000,
            pointerEvents: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          <DragOutlined />
          <Text style={{ color: 'white', fontSize: '12px' }}>
            {dragInfo.draggedComponent?.getName?.() || '组件'}
          </Text>
        </div>
      )}

      {/* 放置区域指示 */}
      {dragInfo.targetComponent && (
        <div
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: dragInfo.canDrop ? 'rgba(24, 144, 255, 0.9)' : 'rgba(255, 77, 79, 0.9)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            zIndex: 10000,
            pointerEvents: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
          }}
        >
          {dragInfo.canDrop ? <AimOutlined /> : <InfoCircleOutlined />}
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
              {dragInfo.canDrop ? '可以放置到' : '无法放置到'}
            </div>
            <div style={{ fontSize: '12px', opacity: 0.9 }}>
              {dragInfo.targetComponent?.getName?.() || '目标组件'}
            </div>
          </div>
        </div>
      )}

      {/* 拖拽提示面板 */}
      <div
        style={{
          position: 'fixed',
          bottom: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'white',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          padding: '12px 16px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          maxWidth: '400px',
        }}
      >
        <DragOutlined style={{ color: '#1890ff' }} />
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
            拖拽操作中
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            将组件拖拽到目标位置，松开鼠标完成放置
          </div>
        </div>
        <div style={{ marginLeft: 'auto' }}>
          <Tag color={dragInfo.canDrop ? 'success' : 'error'}>
            {dragInfo.canDrop ? '可放置' : '不可放置'}
          </Tag>
        </div>
      </div>
    </>
  );
};

// 拖拽网格辅助线组件
export const DragGrid: React.FC<{ editor?: any; isVisible?: boolean }> = ({
  editor,
  isVisible = false,
}) => {
  const [showGrid, setShowGrid] = useState(false);

  useEffect(() => {
    if (!editor) return;

    const handleDragStart = () => setShowGrid(true);
    const handleDragEnd = () => setShowGrid(false);

    editor.on('block:drag:start', handleDragStart);
    editor.on('component:drag:start', handleDragStart);
    editor.on('block:drag:stop', handleDragEnd);
    editor.on('component:drag:end', handleDragEnd);

    return () => {
      editor.off('block:drag:start', handleDragStart);
      editor.off('component:drag:start', handleDragStart);
      editor.off('block:drag:stop', handleDragEnd);
      editor.off('component:drag:end', handleDragEnd);
    };
  }, [editor]);

  if (!isVisible || !showGrid) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        pointerEvents: 'none',
        zIndex: 999,
        backgroundImage: `
          linear-gradient(rgba(24, 144, 255, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(24, 144, 255, 0.1) 1px, transparent 1px)
        `,
        backgroundSize: '20px 20px',
      }}
    />
  );
};

// 组件边界高亮
export const ComponentHighlight: React.FC<{ editor?: any }> = ({ editor }) => {
  const [hoveredComponent, setHoveredComponent] = useState<any>(null);
  const [selectedComponent, setSelectedComponent] = useState<any>(null);

  useEffect(() => {
    if (!editor) return;

    const handleHover = (component: any) => {
      setHoveredComponent(component);
    };

    const handleUnhover = () => {
      setHoveredComponent(null);
    };

    const handleSelect = (component: any) => {
      setSelectedComponent(component);
    };

    const handleDeselect = () => {
      setSelectedComponent(null);
    };

    editor.on('component:hover', handleHover);
    editor.on('component:unhover', handleUnhover);
    editor.on('component:selected', handleSelect);
    editor.on('component:deselected', handleDeselect);

    return () => {
      editor.off('component:hover', handleHover);
      editor.off('component:unhover', handleUnhover);
      editor.off('component:selected', handleSelect);
      editor.off('component:deselected', handleDeselect);
    };
  }, [editor]);

  return (
    <>
      {/* 悬停高亮 */}
      {hoveredComponent && (
        <div
          style={{
            position: 'fixed',
            top: '10px',
            left: '10px',
            background: 'rgba(24, 144, 255, 0.9)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: 1001,
            pointerEvents: 'none',
          }}
        >
          悬停: {hoveredComponent.getName?.() || hoveredComponent.get('tagName') || '组件'}
        </div>
      )}

      {/* 选中高亮 */}
      {selectedComponent && (
        <div
          style={{
            position: 'fixed',
            top: '10px',
            left: '150px',
            background: 'rgba(82, 196, 26, 0.9)',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: 1001,
            pointerEvents: 'none',
          }}
        >
          选中: {selectedComponent.getName?.() || selectedComponent.get('tagName') || '组件'}
        </div>
      )}
    </>
  );
};
