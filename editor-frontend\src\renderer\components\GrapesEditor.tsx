import {
  ClearOutlined,
  CodeOutlined,
  EyeOutlined,
  RedoOutlined,
  SaveOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import { Button, Card, message, Space, Spin, Typography } from 'antd';
import grapesjs from 'grapesjs';
import React, { useEffect, useRef, useState } from 'react';

import { useDialogs, useElectronAPI, useFileOperations } from '../hooks/useElectronAPI';

import 'grapesjs/dist/css/grapes.min.css';
import './GrapesEditor.css';

const { Text } = Typography;

interface GrapesEditorProps {
  onSave?: (html: string, css: string) => void;
  onLoad?: () => void;
  initialContent?: {
    html?: string;
    css?: string;
  };
  height?: string | number;
}

export const GrapesEditor: React.FC<GrapesEditorProps> = ({
  onSave,
  onLoad,
  initialContent,
  height = '600px',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [editor, setEditor] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentFile, setCurrentFile] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<any>(null);
  const [isShortcutsVisible, setIsShortcutsVisible] = useState(false);
  const [showDragGrid, setShowDragGrid] = useState(false);

  const electronAPI = useElectronAPI();
  const fileOps = useFileOperations();
  const dialogs = useDialogs();

  // 初始化 GrapesJS 编辑器
  useEffect(() => {
    if (!editorRef.current) return;

    const editorInstance = grapesjs.init({
      container: editorRef.current,
      height: typeof height === 'number' ? `${height}px` : height,
      width: '100%',

      // 基础配置
      fromElement: false,
      showOffsets: true,
      noticeOnUnload: false,

      // 存储配置
      storageManager: {
        type: 'local',
        autosave: false,
        autoload: false,
      },

      // 资源管理器配置
      assetManager: {
        embedAsBase64: true,
        upload: false,
        uploadText: '拖拽文件到这里或点击上传',
        addBtnText: '添加图片',
        modalTitle: '选择图片',
      },

      // 面板配置
      panels: {
        defaults: [
          {
            id: 'layers',
            el: '.panel__right',
            resizable: {
              maxDim: 350,
              minDim: 200,
              tc: 0,
              cl: 1,
              cr: 0,
              bc: 0,
              keyWidth: 'flex-basis',
            },
          },
          {
            id: 'panel-switcher',
            el: '.panel__switcher',
            buttons: [
              {
                id: 'show-layers',
                active: true,
                label: '图层',
                command: 'show-layers',
                togglable: false,
              },
              {
                id: 'show-style',
                active: true,
                label: '样式',
                command: 'show-styles',
                togglable: false,
              },
              {
                id: 'show-traits',
                active: true,
                label: '属性',
                command: 'show-traits',
                togglable: false,
              },
            ],
          },
        ],
      },

      // 设备管理器配置
      deviceManager: {
        devices: [
          {
            name: '桌面',
            width: '',
          },
          {
            name: '平板',
            width: '768px',
            widthMedia: '992px',
          },
          {
            name: '手机',
            width: '320px',
            widthMedia: '768px',
          },
        ],
      },

      // 块管理器配置
      blockManager: {
        appendTo: '.blocks-container',
        blocks: [
          // 基础组件
          {
            id: 'section',
            label: '区域',
            category: '布局',
            attributes: { class: 'gjs-block-section' },
            content: `<section style="padding: 20px; margin: 10px 0; border: 2px dashed #ddd;">
              <h2 style="margin: 0 0 10px 0; color: #333;">区域标题</h2>
              <p style="margin: 0; color: #666;">在这里添加你的内容</p>
            </section>`,
          },
          {
            id: 'container',
            label: '容器',
            category: '布局',
            content: `<div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
              <p>容器内容</p>
            </div>`,
          },
          {
            id: 'row',
            label: '行',
            category: '布局',
            content: `<div style="display: flex; flex-wrap: wrap; margin: -10px;">
              <div style="flex: 1; padding: 10px; min-width: 300px;">
                <p>列 1</p>
              </div>
              <div style="flex: 1; padding: 10px; min-width: 300px;">
                <p>列 2</p>
              </div>
            </div>`,
          },
          {
            id: 'column',
            label: '列',
            category: '布局',
            content: `<div style="flex: 1; padding: 15px; min-height: 100px; border: 1px dashed #ddd;">
              <p>列内容</p>
            </div>`,
          },

          // 文本组件
          {
            id: 'text',
            label: '文本',
            category: '文本',
            content: '<div data-gjs-type="text">插入你的文本内容</div>',
          },
          {
            id: 'heading',
            label: '标题',
            category: '文本',
            content: '<h1 style="margin: 0; color: #333;">标题文本</h1>',
          },
          {
            id: 'paragraph',
            label: '段落',
            category: '文本',
            content:
              '<p style="line-height: 1.6; color: #666;">这是一个段落文本，你可以在这里添加更多的内容描述。</p>',
          },
          {
            id: 'quote',
            label: '引用',
            category: '文本',
            content: `<blockquote style="border-left: 4px solid #1890ff; padding-left: 16px; margin: 16px 0; font-style: italic; color: #666;">
              "这是一个引用文本示例"
            </blockquote>`,
          },

          // 媒体组件
          {
            id: 'image',
            label: '图片',
            category: '媒体',
            select: true,
            content: {
              type: 'image',
              style: {
                'max-width': '100%',
                height: 'auto',
              },
            },
            activate: true,
          },
          {
            id: 'video',
            label: '视频',
            category: '媒体',
            content: {
              type: 'video',
              src: '',
              style: {
                width: '100%',
                'max-width': '600px',
                height: 'auto',
              },
            },
          },
          {
            id: 'iframe',
            label: '嵌入',
            category: '媒体',
            content: `<iframe src="about:blank" style="width: 100%; height: 300px; border: 1px solid #ddd;" frameborder="0">
            </iframe>`,
          },

          // 交互组件
          {
            id: 'link',
            label: '链接',
            category: '交互',
            content: '<a href="#" style="color: #1890ff; text-decoration: none;">链接文本</a>',
          },
          {
            id: 'button',
            label: '按钮',
            category: '交互',
            content: `<button style="
              background-color: #1890ff;
              color: white;
              border: none;
              padding: 8px 16px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
            ">按钮文本</button>`,
          },
          {
            id: 'button-group',
            label: '按钮组',
            category: '交互',
            content: `<div style="display: flex; gap: 8px;">
              <button style="background-color: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">主要</button>
              <button style="background-color: #f5f5f5; color: #333; border: 1px solid #d9d9d9; padding: 8px 16px; border-radius: 4px; cursor: pointer;">次要</button>
            </div>`,
          },

          // 列表组件
          {
            id: 'list',
            label: '无序列表',
            category: '列表',
            content: `<ul style="padding-left: 20px; line-height: 1.6;">
              <li>列表项 1</li>
              <li>列表项 2</li>
              <li>列表项 3</li>
            </ul>`,
          },
          {
            id: 'ordered-list',
            label: '有序列表',
            category: '列表',
            content: `<ol style="padding-left: 20px; line-height: 1.6;">
              <li>第一项</li>
              <li>第二项</li>
              <li>第三项</li>
            </ol>`,
          },
          {
            id: 'definition-list',
            label: '定义列表',
            category: '列表',
            content: `<dl style="line-height: 1.6;">
              <dt style="font-weight: bold; margin-top: 10px;">术语 1</dt>
              <dd style="margin-left: 20px; color: #666;">术语 1 的定义</dd>
              <dt style="font-weight: bold; margin-top: 10px;">术语 2</dt>
              <dd style="margin-left: 20px; color: #666;">术语 2 的定义</dd>
            </dl>`,
          },

          // 表单组件
          {
            id: 'input',
            label: '输入框',
            category: '表单',
            content: `<input type="text" placeholder="请输入内容" style="
              width: 100%;
              padding: 8px 12px;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              font-size: 14px;
            ">`,
          },
          {
            id: 'textarea',
            label: '文本域',
            category: '表单',
            content: `<textarea placeholder="请输入多行文本" rows="4" style="
              width: 100%;
              padding: 8px 12px;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              font-size: 14px;
              resize: vertical;
            "></textarea>`,
          },
          {
            id: 'select',
            label: '下拉选择',
            category: '表单',
            content: `<select style="
              width: 100%;
              padding: 8px 12px;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              font-size: 14px;
            ">
              <option>选项 1</option>
              <option>选项 2</option>
              <option>选项 3</option>
            </select>`,
          },
          {
            id: 'checkbox',
            label: '复选框',
            category: '表单',
            content: `<label style="display: flex; align-items: center; cursor: pointer;">
              <input type="checkbox" style="margin-right: 8px;">
              <span>复选框选项</span>
            </label>`,
          },
          {
            id: 'radio',
            label: '单选框',
            category: '表单',
            content: `<div>
              <label style="display: flex; align-items: center; cursor: pointer; margin-bottom: 8px;">
                <input type="radio" name="radio-group" style="margin-right: 8px;">
                <span>选项 1</span>
              </label>
              <label style="display: flex; align-items: center; cursor: pointer;">
                <input type="radio" name="radio-group" style="margin-right: 8px;">
                <span>选项 2</span>
              </label>
            </div>`,
          },

          // 分隔组件
          {
            id: 'divider',
            label: '分割线',
            category: '装饰',
            content: '<hr style="border: none; border-top: 1px solid #e8e8e8; margin: 20px 0;">',
          },
          {
            id: 'spacer',
            label: '间距',
            category: '装饰',
            content: '<div style="height: 40px;"></div>',
          },

          // 卡片组件
          {
            id: 'card',
            label: '卡片',
            category: '容器',
            content: `<div style="
              background: white;
              border: 1px solid #e8e8e8;
              border-radius: 8px;
              padding: 20px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              margin: 16px 0;
            ">
              <h3 style="margin: 0 0 12px 0; color: #333;">卡片标题</h3>
              <p style="margin: 0; color: #666; line-height: 1.6;">卡片内容描述</p>
            </div>`,
          },
          {
            id: 'alert',
            label: '提示框',
            category: '容器',
            content: `<div style="
              background-color: #e6f7ff;
              border: 1px solid #91d5ff;
              border-radius: 4px;
              padding: 12px 16px;
              margin: 16px 0;
              color: #0050b3;
            ">
              <strong>提示：</strong>这是一个信息提示框
            </div>`,
          },
        ],
      },

      // 样式管理器配置
      styleManager: {
        appendTo: '.styles-container',
        sectors: [
          {
            name: '尺寸',
            open: false,
            buildProps: ['width', 'min-height', 'padding'],
            properties: [
              {
                type: 'integer',
                name: '宽度',
                property: 'width',
                units: ['px', '%'],
                defaults: 'auto',
                min: 0,
              },
            ],
          },
          {
            name: '装饰',
            open: false,
            buildProps: [
              'opacity',
              'background-color',
              'border-radius',
              'border',
              'box-shadow',
              'background',
            ],
          },
          {
            name: '排版',
            open: false,
            buildProps: [
              'font-family',
              'font-size',
              'font-weight',
              'letter-spacing',
              'color',
              'line-height',
              'text-align',
              'text-decoration',
              'text-shadow',
            ],
          },
          {
            name: '布局',
            open: false,
            buildProps: [
              'display',
              'position',
              'top',
              'right',
              'left',
              'bottom',
              'margin',
              'padding',
              'float',
              'clear',
            ],
          },
        ],
      },

      // 特征管理器配置
      traitManager: {
        appendTo: '.traits-container',
      },

      // 图层管理器配置
      layerManager: {
        appendTo: '.layers-container',
      },

      // 画布配置
      canvas: {
        styles: ['https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css'],
        scripts: [],
      },

      // 拖拽管理器配置
      dragMode: 'absolute',

      // 选择器管理器配置
      selectorManager: {
        appendTo: '.styles-container',
        escapeName: true,
      },

      // 命令管理器配置
      commands: {
        defaults: [
          {
            id: 'set-device-desktop',
            run: editor => editor.setDevice('Desktop'),
          },
          {
            id: 'set-device-tablet',
            run: editor => editor.setDevice('Tablet'),
          },
          {
            id: 'set-device-mobile',
            run: editor => editor.setDevice('Mobile'),
          },
          {
            id: 'sw-visibility',
            run: editor => {
              const selected = editor.getSelected();
              if (selected) {
                const isVisible = selected.getStyle('display') !== 'none';
                selected.setStyle({ display: isVisible ? 'none' : 'block' });
              }
            },
          },
          {
            id: 'duplicate',
            run: editor => {
              const selected = editor.getSelected();
              if (selected) {
                const cloned = selected.clone();
                selected.parent().append(cloned);
                editor.select(cloned);
              }
            },
          },
          {
            id: 'move-up',
            run: editor => {
              const selected = editor.getSelected();
              if (selected) {
                const parent = selected.parent();
                const index = parent.components().indexOf(selected);
                if (index > 0) {
                  parent.components().remove(selected);
                  parent.components().add(selected, { at: index - 1 });
                }
              }
            },
          },
          {
            id: 'move-down',
            run: editor => {
              const selected = editor.getSelected();
              if (selected) {
                const parent = selected.parent();
                const components = parent.components();
                const index = components.indexOf(selected);
                if (index < components.length - 1) {
                  components.remove(selected);
                  components.add(selected, { at: index + 1 });
                }
              }
            },
          },
        ],
      },

      // 键盘快捷键配置
      keymaps: {
        defaults: {
          'core:undo': 'ctrl+z',
          'core:redo': 'ctrl+shift+z',
          'core:copy': 'ctrl+c',
          'core:paste': 'ctrl+v',
          'core:component-delete': ['delete', 'backspace'],
          duplicate: 'ctrl+d',
          'sw-visibility': 'ctrl+shift+h',
          'move-up': 'ctrl+up',
          'move-down': 'ctrl+down',
        },
      },
    });

    // 设置初始内容
    if (initialContent) {
      if (initialContent.html) {
        editorInstance.setComponents(initialContent.html);
      }
      if (initialContent.css) {
        editorInstance.setStyle(initialContent.css);
      }
    }

    // 监听变化
    editorInstance.on('change:changesCount', () => {
      setHasUnsavedChanges(true);
    });

    // 监听组件选择
    editorInstance.on('component:selected', (component: any) => {
      setSelectedComponent(component);
    });

    editorInstance.on('component:deselected', () => {
      setSelectedComponent(null);
    });

    setEditor(editorInstance);
    setIsLoading(false);

    if (onLoad) {
      onLoad();
    }

    // 清理函数
    return () => {
      if (editorInstance) {
        editorInstance.destroy();
      }
    };
  }, [height, initialContent, onLoad]);

  // 保存文件
  const handleSave = async () => {
    if (!editor) return;

    try {
      const html = editor.getHtml();
      const css = editor.getCss();

      if (onSave) {
        onSave(html, css);
      }

      if (currentFile) {
        // 保存到当前文件
        const content = JSON.stringify({ html, css }, null, 2);
        await fileOps.writeFile(currentFile, content);
        message.success('文件保存成功');
        setHasUnsavedChanges(false);
      } else {
        // 另存为
        await handleSaveAs();
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 另存为
  const handleSaveAs = async () => {
    if (!editor) return;

    try {
      const result = await dialogs.showSaveDialog({
        title: '保存项目',
        defaultPath: 'project.json',
        filters: [
          { name: '项目文件', extensions: ['json'] },
          { name: '所有文件', extensions: ['*'] },
        ],
      });

      if (!result.canceled && result.filePath) {
        const html = editor.getHtml();
        const css = editor.getCss();
        const content = JSON.stringify({ html, css }, null, 2);

        await fileOps.writeFile(result.filePath, content);
        setCurrentFile(result.filePath);
        setHasUnsavedChanges(false);
        message.success('文件保存成功');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 打开文件
  const handleOpen = async () => {
    try {
      const result = await dialogs.showOpenDialog({
        title: '打开项目',
        filters: [
          { name: '项目文件', extensions: ['json'] },
          { name: '所有文件', extensions: ['*'] },
        ],
        properties: ['openFile'],
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        const content = await fileOps.readFile(filePath);

        try {
          const data = JSON.parse(content);
          if (editor) {
            editor.setComponents(data.html || '');
            editor.setStyle(data.css || '');
            setCurrentFile(filePath);
            setHasUnsavedChanges(false);
            message.success('文件打开成功');
          }
        } catch (parseError) {
          message.error('文件格式错误');
        }
      }
    } catch (error) {
      console.error('打开文件失败:', error);
      message.error('打开文件失败');
    }
  };

  // 预览
  const handlePreview = () => {
    if (!editor) return;

    const html = editor.getHtml();
    const css = editor.getCss();
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>预览</title>
          <style>${css}</style>
        </head>
        <body>${html}</body>
      </html>
    `;

    // 在新窗口中打开预览
    const previewWindow = window.open('', '_blank');
    if (previewWindow) {
      previewWindow.document.write(fullHtml);
      previewWindow.document.close();
    }
  };

  // 查看代码
  const handleViewCode = () => {
    if (!editor) return;

    const html = editor.getHtml();
    const css = editor.getCss();

    console.log('HTML:', html);
    console.log('CSS:', css);

    message.info('代码已输出到控制台');
  };

  // 撤销
  const handleUndo = () => {
    if (editor) {
      editor.UndoManager.undo();
    }
  };

  // 重做
  const handleRedo = () => {
    if (editor) {
      editor.UndoManager.redo();
    }
  };

  // 清空
  const handleClear = () => {
    if (editor) {
      editor.setComponents('');
      editor.setStyle('');
      setHasUnsavedChanges(true);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size='large' />
          <div style={{ marginTop: '16px' }}>
            <Text>正在加载编辑器...</Text>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className='grapes-editor-container'>
      {/* 工具栏 */}
      <Card size='small' style={{ marginBottom: '8px' }}>
        <Space>
          <Button icon={<SaveOutlined />} onClick={handleSave} type='primary'>
            保存 {hasUnsavedChanges && '*'}
          </Button>
          <Button onClick={handleOpen}>打开</Button>
          <Button onClick={handleSaveAs}>另存为</Button>
          <Button icon={<EyeOutlined />} onClick={handlePreview}>
            预览
          </Button>
          <Button icon={<CodeOutlined />} onClick={handleViewCode}>
            查看代码
          </Button>
          <Button icon={<UndoOutlined />} onClick={handleUndo}>
            撤销
          </Button>
          <Button icon={<RedoOutlined />} onClick={handleRedo}>
            重做
          </Button>
          <Button icon={<ClearOutlined />} onClick={handleClear} danger>
            清空
          </Button>
          <Button icon={<KeyboardOutlined />} onClick={() => setIsShortcutsVisible(true)}>
            快捷键
          </Button>
        </Space>
      </Card>

      {/* 编辑器容器 */}
      <div className='grapes-editor-wrapper'>
        <div ref={editorRef} className='grapes-editor' />
      </div>

      {/* 编辑工具栏 */}
      <EditingToolbar
        selectedComponent={selectedComponent}
        editor={editor}
        onComponentChange={() => setHasUnsavedChanges(true)}
      />

      {/* 拖拽辅助 */}
      <DragHelper editor={editor} />
      <DragGrid editor={editor} isVisible={showDragGrid} />
      <ComponentHighlight editor={editor} />

      {/* 快捷键面板 */}
      <KeyboardShortcuts
        editor={editor}
        visible={isShortcutsVisible}
        onClose={() => setIsShortcutsVisible(false)}
      />
    </div>
  );
};
