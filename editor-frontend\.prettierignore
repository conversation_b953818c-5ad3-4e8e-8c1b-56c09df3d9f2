# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
out/
build/
coverage/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Electron
app/
release/

# Logs
*.log

# Package files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
*.d.ts.map
*.js.map

# Config files that should not be formatted
electron-builder.yml
