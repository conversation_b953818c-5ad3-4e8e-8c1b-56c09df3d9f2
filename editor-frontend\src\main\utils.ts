import { app } from 'electron';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// ES 模块中获取 __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 判断是否为开发环境
 */
export const isDev = process.env['NODE_ENV'] === 'development' || !app.isPackaged;

/**
 * 获取预加载脚本路径
 */
export const getPreloadPath = (): string => {
  return join(__dirname, 'preload.js');
};

/**
 * 获取 UI 路径
 */
export const getUIPath = (): string => {
  if (isDev) {
    return 'http://localhost:5173';
  }
  return join(__dirname, '../renderer/index.html');
};

/**
 * 获取资源路径
 * @param relativePath 相对路径
 * @returns 绝对路径
 */
export const getAssetPath = (relativePath: string): string => {
  if (isDev) {
    return `${__dirname}/../../../assets/${relativePath}`;
  }
  return `${process.resourcesPath}/assets/${relativePath}`;
};

/**
 * 日志记录工具
 */
export const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[INFO] ${new Date().toISOString()} - ${message}`, ...args);
  },

  error: (message: string, error?: Error, ...args: any[]) => {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error, ...args);
  },

  warn: (message: string, ...args: any[]) => {
    console.warn(`[WARN] ${new Date().toISOString()} - ${message}`, ...args);
  },

  debug: (message: string, ...args: any[]) => {
    if (isDev) {
      console.debug(`[DEBUG] ${new Date().toISOString()} - ${message}`, ...args);
    }
  },
};

/**
 * 延迟执行
 * @param ms 毫秒数
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 安全的 JSON 解析
 * @param jsonString JSON 字符串
 * @param defaultValue 默认值
 */
export const safeJsonParse = <T>(jsonString: string, defaultValue: T): T => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    logger.error('JSON 解析失败', error as Error);
    return defaultValue;
  }
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * 验证文件扩展名
 * @param filename 文件名
 * @param allowedExtensions 允许的扩展名数组
 */
export const validateFileExtension = (filename: string, allowedExtensions: string[]): boolean => {
  const extension = filename.toLowerCase().split('.').pop();
  return extension ? allowedExtensions.includes(extension) : false;
};

/**
 * 生成唯一 ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }

  return obj;
};
