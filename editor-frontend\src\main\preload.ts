import { context<PERSON><PERSON>, ipc<PERSON><PERSON>er } from 'electron';
import type {
    AppInfo,
    AppSettings,
    NotificationOptions,
    PerformanceMetrics,
    WindowState
} from '../shared/types';

// 定义暴露给渲染进程的完整 API 类型
export interface ElectronAPI {
  // 应用相关
  app: {
    getVersion: () => Promise<string>;
    getPath: (name: string) => Promise<string>;
    getInfo: () => Promise<AppInfo>;
    quit: () => void;
    restart: () => void;
    getPerformanceMetrics: () => Promise<PerformanceMetrics>;
  };

  // 窗口相关
  window: {
    minimize: () => void;
    maximize: () => void;
    unmaximize: () => void;
    close: () => void;
    setTitle: (title: string) => void;
    setSize: (width: number, height: number) => void;
    center: () => void;
    getState: () => Promise<WindowState>;
    onFocus: (callback: () => void) => void;
    onBlur: (callback: () => void) => void;
    onMaximize: (callback: () => void) => void;
    onUnmaximize: (callback: () => void) => void;
    onMinimize: (callback: () => void) => void;
    onRestore: (callback: () => void) => void;
  };

  // 对话框相关
  dialog: {
    showMessage: (options: Electron.MessageBoxOptions) => Promise<Electron.MessageBoxReturnValue>;
    showError: (title: string, content: string) => void;
    showOpen: (options: Electron.OpenDialogOptions) => Promise<Electron.OpenDialogReturnValue>;
    showSave: (options: Electron.SaveDialogOptions) => Promise<Electron.SaveDialogReturnValue>;
  };

  // 文件系统相关
  file: {
    read: (filePath: string) => Promise<string>;
    write: (filePath: string, content: string) => Promise<void>;
    exists: (filePath: string) => Promise<boolean>;
    delete: (filePath: string) => Promise<void>;
    copy: (src: string, dest: string) => Promise<void>;
    move: (src: string, dest: string) => Promise<void>;
  };

  // 文件夹相关
  folder: {
    create: (folderPath: string) => Promise<void>;
    read: (folderPath: string) => Promise<string[]>;
  };

  // Shell 相关
  shell: {
    openExternal: (url: string) => Promise<void>;
    showItemInFolder: (fullPath: string) => void;
    openPath: (path: string) => Promise<string>;
  };

  // 菜单事件监听
  menu: {
    onNewBook: (callback: () => void) => void;
    onOpenBook: (callback: (filePath: string) => void) => void;
    onSaveBook: (callback: () => void) => void;
  };

  // 通知相关
  notification: {
    show: (options: NotificationOptions) => void;
  };

  // 设置相关
  settings: {
    get: () => Promise<AppSettings>;
    set: (settings: Partial<AppSettings>) => Promise<void>;
    reset: () => Promise<void>;
  };

  // 系统信息
  system: {
    platform: string;
    arch: string;
  };

  // 开发工具
  devTools: {
    isOpen: () => Promise<boolean>;
    open: () => void;
    close: () => void;
    toggle: () => void;
  };
}

// 暴露受保护的方法给渲染进程
const electronAPI: ElectronAPI = {
  // 应用相关
  app: {
    getVersion: () => ipcRenderer.invoke('app:get-version'),
    getPath: name => ipcRenderer.invoke('app:get-path', name),
    getInfo: () => ipcRenderer.invoke('app:get-info'),
    quit: () => ipcRenderer.invoke('app:quit'),
    restart: () => ipcRenderer.invoke('app:restart'),
    getPerformanceMetrics: () => ipcRenderer.invoke('app:get-performance-metrics'),
  },

  // 窗口相关
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    unmaximize: () => ipcRenderer.invoke('window:unmaximize'),
    close: () => ipcRenderer.invoke('window:close'),
    setTitle: title => ipcRenderer.invoke('window:set-title', title),
    setSize: (width, height) => ipcRenderer.invoke('window:set-size', width, height),
    center: () => ipcRenderer.invoke('window:center'),
    getState: () => ipcRenderer.invoke('window:get-state'),
    onFocus: callback => ipcRenderer.on('window:focus', callback),
    onBlur: callback => ipcRenderer.on('window:blur', callback),
    onMaximize: callback => ipcRenderer.on('window:maximize', callback),
    onUnmaximize: callback => ipcRenderer.on('window:unmaximize', callback),
    onMinimize: callback => ipcRenderer.on('window:minimize', callback),
    onRestore: callback => ipcRenderer.on('window:restore', callback),
  },

  // 对话框相关
  dialog: {
    showMessage: options => ipcRenderer.invoke('dialog:show-message', options),
    showError: (title, content) => ipcRenderer.invoke('dialog:show-error', title, content),
    showOpen: options => ipcRenderer.invoke('dialog:show-open', options),
    showSave: options => ipcRenderer.invoke('dialog:show-save', options),
  },

  // 文件系统相关
  file: {
    read: filePath => ipcRenderer.invoke('file:read', filePath),
    write: (filePath, content) => ipcRenderer.invoke('file:write', filePath, content),
    exists: filePath => ipcRenderer.invoke('file:exists', filePath),
    delete: filePath => ipcRenderer.invoke('file:delete', filePath),
    copy: (src, dest) => ipcRenderer.invoke('file:copy', src, dest),
    move: (src, dest) => ipcRenderer.invoke('file:move', src, dest),
  },

  // 文件夹相关
  folder: {
    create: folderPath => ipcRenderer.invoke('folder:create', folderPath),
    read: folderPath => ipcRenderer.invoke('folder:read', folderPath),
  },

  // Shell 相关
  shell: {
    openExternal: url => ipcRenderer.invoke('shell:open-external', url),
    showItemInFolder: fullPath => ipcRenderer.invoke('shell:show-item-in-folder', fullPath),
    openPath: path => ipcRenderer.invoke('shell:open-path', path),
  },

  // 菜单事件监听
  menu: {
    onNewBook: callback => ipcRenderer.on('menu:new-book', callback),
    onOpenBook: callback => ipcRenderer.on('menu:open-book', (_, filePath) => callback(filePath)),
    onSaveBook: callback => ipcRenderer.on('menu:save-book', callback),
  },

  // 通知相关
  notification: {
    show: options => ipcRenderer.invoke('notification:show', options),
  },

  // 设置相关
  settings: {
    get: () => ipcRenderer.invoke('settings:get'),
    set: settings => ipcRenderer.invoke('settings:set', settings),
    reset: () => ipcRenderer.invoke('settings:reset'),
  },

  // 系统信息
  system: {
    platform: process.platform,
    arch: process.arch,
  },

  // 开发工具
  devTools: {
    isOpen: () => ipcRenderer.invoke('dev-tools:is-open'),
    open: () => ipcRenderer.invoke('dev-tools:open'),
    close: () => ipcRenderer.invoke('dev-tools:close'),
    toggle: () => ipcRenderer.invoke('dev-tools:toggle'),
  },
};

// 在渲染进程中暴露 electronAPI
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明，供 TypeScript 使用
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// 移除默认的菜单快捷键处理
if (typeof window !== 'undefined') {
  window.addEventListener('DOMContentLoaded', () => {
    // 禁用默认的快捷键
    document.addEventListener('keydown', (e: KeyboardEvent) => {
      // 禁用 F5 刷新
      if (e.key === 'F5') {
        e.preventDefault();
      }

      // 禁用 Ctrl+R 刷新
      if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
      }

      // 禁用 Ctrl+Shift+R 强制刷新
      if (e.ctrlKey && e.shiftKey && e.key === 'R') {
        e.preventDefault();
      }

      // 禁用 Ctrl+Shift+I 开发者工具
      if (e.ctrlKey && e.shiftKey && e.key === 'I') {
        e.preventDefault();
      }
    });
  });
}

// 日志记录
console.log('Preload script loaded successfully');
