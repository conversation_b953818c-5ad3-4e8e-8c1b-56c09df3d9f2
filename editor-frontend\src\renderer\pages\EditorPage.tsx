import {
  AppstoreOutlined,
  ArrowLeftOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Button, Layout, message, Modal, Space, Typography } from 'antd';
import React, { useCallback, useState } from 'react';

import { GrapesEditor } from '../components/GrapesEditor';
import { useElectronAPI } from '../hooks/useElectronAPI';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

interface EditorPageProps {
  onBack?: () => void;
}

export const EditorPage: React.FC<EditorPageProps> = ({ onBack }) => {
  const [isHelpVisible, setIsHelpVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState<string | null>(null);
  const [isTemplatesVisible, setIsTemplatesVisible] = useState(false);
  const [isComponentPanelVisible, setIsComponentPanelVisible] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<any>(null);
  const electronAPI = useElectronAPI();

  const handleSave = useCallback((html: string, css: string) => {
    console.log('保存内容:', { html, css });
    message.success('内容已保存');
  }, []);

  const handleLoad = useCallback(() => {
    console.log('编辑器加载完成');
    message.info('编辑器已准备就绪');
  }, []);

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  const showHelp = () => {
    setIsHelpVisible(true);
  };

  const showTemplates = () => {
    setIsTemplatesVisible(true);
  };

  const showComponentPanel = () => {
    setIsComponentPanelVisible(true);
  };

  const handleUseTemplate = useCallback((template: any) => {
    // 这里可以将模板添加到编辑器中
    console.log('使用模板:', template);
    message.success(`已添加模板：${template.name}`);
    setIsTemplatesVisible(false);
  }, []);

  const handleComponentSelect = useCallback((component: any) => {
    setSelectedComponent(component);
    setIsComponentPanelVisible(true);
  }, []);

  const handleStyleChange = useCallback(
    (styles: any) => {
      if (selectedComponent) {
        selectedComponent.setStyle(styles);
      }
    },
    [selectedComponent]
  );

  const handleAttributeChange = useCallback(
    (attributes: any) => {
      if (selectedComponent) {
        selectedComponent.setAttributes(attributes);
      }
    },
    [selectedComponent]
  );

  const handleAbout = async () => {
    if (electronAPI) {
      try {
        await electronAPI.dialog.showMessage({
          type: 'info',
          title: '关于编辑器',
          message: '桌面端图书编辑器',
          detail:
            '基于 GrapesJS 的可视化网页编辑器\n\n功能特性：\n• 拖拽式编辑\n• 响应式设计\n• 实时预览\n• 代码导出\n• 项目保存',
          buttons: ['确定'],
        });
      } catch (error) {
        console.error('显示关于对话框失败:', error);
      }
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header
        style={{
          background: '#001529',
          padding: '0 24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button
            type='text'
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            style={{ color: 'white', marginRight: '16px' }}
          >
            返回
          </Button>
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            可视化编辑器
          </Title>
          {currentProject && (
            <Text style={{ color: 'rgba(255, 255, 255, 0.65)', marginLeft: '16px' }}>
              {currentProject}
            </Text>
          )}
        </div>

        <Space>
          <Button
            type='text'
            icon={<AppstoreOutlined />}
            onClick={showTemplates}
            style={{ color: 'white' }}
          >
            模板
          </Button>
          <Button
            type='text'
            icon={<SettingOutlined />}
            onClick={showComponentPanel}
            style={{ color: 'white' }}
          >
            属性
          </Button>
          <Button
            type='text'
            icon={<QuestionCircleOutlined />}
            onClick={showHelp}
            style={{ color: 'white' }}
          >
            帮助
          </Button>
          <Button
            type='text'
            icon={<InfoCircleOutlined />}
            onClick={handleAbout}
            style={{ color: 'white' }}
          >
            关于
          </Button>
        </Space>
      </Header>

      <Content style={{ padding: '16px', background: '#f0f2f5' }}>
        <div style={{ maxWidth: '100%', margin: '0 auto' }}>
          <GrapesEditor onSave={handleSave} onLoad={handleLoad} height='calc(100vh - 120px)' />
        </div>
      </Content>

      {/* 帮助模态框 */}
      <Modal
        title='编辑器使用帮助'
        open={isHelpVisible}
        onCancel={() => setIsHelpVisible(false)}
        footer={[
          <Button key='close' onClick={() => setIsHelpVisible(false)}>
            关闭
          </Button>,
        ]}
        width={600}
      >
        <div>
          <Title level={5}>基本操作</Title>
          <ul>
            <li>
              <strong>拖拽组件：</strong>从左侧组件面板拖拽元素到画布中
            </li>
            <li>
              <strong>选择元素：</strong>点击画布中的元素进行选择
            </li>
            <li>
              <strong>编辑样式：</strong>选择元素后在右侧样式面板中修改样式
            </li>
            <li>
              <strong>编辑属性：</strong>在属性面板中修改元素的属性
            </li>
            <li>
              <strong>查看图层：</strong>在图层面板中查看和管理页面结构
            </li>
          </ul>

          <Title level={5}>工具栏功能</Title>
          <ul>
            <li>
              <strong>保存：</strong>保存当前项目到文件
            </li>
            <li>
              <strong>打开：</strong>打开已保存的项目文件
            </li>
            <li>
              <strong>另存为：</strong>将项目保存为新文件
            </li>
            <li>
              <strong>预览：</strong>在新窗口中预览页面效果
            </li>
            <li>
              <strong>查看代码：</strong>查看生成的 HTML 和 CSS 代码
            </li>
            <li>
              <strong>撤销/重做：</strong>撤销或重做最近的操作
            </li>
            <li>
              <strong>清空：</strong>清空画布中的所有内容
            </li>
          </ul>

          <Title level={5}>响应式设计</Title>
          <ul>
            <li>
              <strong>设备切换：</strong>使用顶部设备切换器预览不同屏幕尺寸
            </li>
            <li>
              <strong>断点设置：</strong>为不同设备设置不同的样式
            </li>
            <li>
              <strong>自适应布局：</strong>使用百分比和弹性布局创建响应式设计
            </li>
          </ul>

          <Title level={5}>快捷键</Title>
          <ul>
            <li>
              <strong>Ctrl+S：</strong>保存项目
            </li>
            <li>
              <strong>Ctrl+Z：</strong>撤销操作
            </li>
            <li>
              <strong>Ctrl+Y：</strong>重做操作
            </li>
            <li>
              <strong>Delete：</strong>删除选中的元素
            </li>
            <li>
              <strong>Ctrl+C：</strong>复制选中的元素
            </li>
            <li>
              <strong>Ctrl+V：</strong>粘贴元素
            </li>
          </ul>

          <Title level={5}>项目文件</Title>
          <ul>
            <li>
              <strong>文件格式：</strong>项目以 JSON 格式保存，包含 HTML 和 CSS
            </li>
            <li>
              <strong>导出选项：</strong>可以导出纯 HTML 文件或完整的网站包
            </li>
            <li>
              <strong>资源管理：</strong>图片和其他资源会被嵌入到项目中
            </li>
          </ul>

          <Title level={5}>技巧和建议</Title>
          <ul>
            <li>定期保存项目以避免数据丢失</li>
            <li>使用图层面板管理复杂的页面结构</li>
            <li>利用组件复用功能提高开发效率</li>
            <li>在不同设备上测试响应式效果</li>
            <li>使用语义化的 HTML 标签提高可访问性</li>
          </ul>
        </div>
      </Modal>

      {/* 模板抽屉 */}
      <Drawer
        title='组件模板'
        placement='right'
        width={600}
        open={isTemplatesVisible}
        onClose={() => setIsTemplatesVisible(false)}
      >
        <ComponentTemplates onUseTemplate={handleUseTemplate} />
      </Drawer>

      {/* 组件属性抽屉 */}
      <Drawer
        title='组件属性'
        placement='right'
        width={400}
        open={isComponentPanelVisible}
        onClose={() => setIsComponentPanelVisible(false)}
      >
        <AdvancedComponentPanel
          selectedComponent={selectedComponent}
          onStyleChange={handleStyleChange}
          onAttributeChange={handleAttributeChange}
        />
      </Drawer>
    </Layout>
  );
};
