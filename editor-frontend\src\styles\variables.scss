// Color Variables
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$info-color: #1890ff;

// Neutral Colors
$white: #ffffff;
$gray-1: #fafafa;
$gray-2: #f5f5f5;
$gray-3: #f0f0f0;
$gray-4: #d9d9d9;
$gray-5: #bfbfbf;
$gray-6: #8c8c8c;
$gray-7: #595959;
$gray-8: #434343;
$gray-9: #262626;
$gray-10: #1f1f1f;
$black: #000000;

// Background Colors
$body-background: #f0f2f5;
$component-background: #ffffff;
$popover-background: #ffffff;
$popover-customize-border-color: #f0f0f0;

// Text Colors
$text-color: rgba(0, 0, 0, 0.85);
$text-color-secondary: rgba(0, 0, 0, 0.65);
$text-color-inverse: #ffffff;
$icon-color: rgba(0, 0, 0, 0.45);
$icon-color-hover: rgba(0, 0, 0, 0.75);
$heading-color: rgba(0, 0, 0, 0.85);
$text-color-dark: #ffffff;
$text-color-secondary-dark: rgba(255, 255, 255, 0.65);

// Border Colors
$border-color-base: #d9d9d9;
$border-color-split: rgba(0, 0, 0, 0.06);
$border-color-inverse: #ffffff;

// Shadow
$shadow-1-up:
  0 -6px 16px -8px rgba(0, 0, 0, 0.08),
  0 -9px 28px 0 rgba(0, 0, 0, 0.05),
  0 -12px 48px 16px rgba(0, 0, 0, 0.03);
$shadow-1-down:
  0 6px 16px -8px rgba(0, 0, 0, 0.08),
  0 9px 28px 0 rgba(0, 0, 0, 0.05),
  0 12px 48px 16px rgba(0, 0, 0, 0.03);
$shadow-1-left:
  -6px 0 16px -8px rgba(0, 0, 0, 0.08),
  -9px 0 28px 0 rgba(0, 0, 0, 0.05),
  -12px 0 48px 16px rgba(0, 0, 0, 0.03);
$shadow-1-right:
  6px 0 16px -8px rgba(0, 0, 0, 0.08),
  9px 0 28px 0 rgba(0, 0, 0, 0.05),
  12px 0 48px 16px rgba(0, 0, 0, 0.03);
$shadow-2:
  0 3px 6px -4px rgba(0, 0, 0, 0.12),
  0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 9px 28px 8px rgba(0, 0, 0, 0.05);

// Layout
$layout-body-background: #f0f2f5;
$layout-header-background: #001529;
$layout-header-height: 64px;
$layout-header-padding: 0 50px;
$layout-footer-padding: 24px 50px;
$layout-sider-background: #001529;
$layout-trigger-height: 48px;
$layout-trigger-background: #002140;
$layout-trigger-color: #ffffff;
$layout-zero-trigger-width: 36px;
$layout-zero-trigger-height: 42px;

// Typography
$font-family:
  -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
  sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
$code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-sm: 12px;
$font-weight-bold: 600;
$line-height-base: 1.5715;

// Spacing
$padding-lg: 24px;
$padding-md: 16px;
$padding-sm: 12px;
$padding-xs: 8px;
$padding-xss: 4px;

$margin-lg: 24px;
$margin-md: 16px;
$margin-sm: 12px;
$margin-xs: 8px;
$margin-xss: 4px;

// Border Radius
$border-radius-base: 6px;
$border-radius-sm: 4px;
$border-radius-lg: 8px;

// Animation
$ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);
$ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);
$ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
$ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
$ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
$ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);
$ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);
$ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);
$ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
$ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);
$ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);
$ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
$ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
$ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);

// Z-index
$zindex-base: 0;
$zindex-affix: 10;
$zindex-back-top: 10;
$zindex-picker-panel: 10;
$zindex-popup-close: 10;
$zindex-modal: 1000;
$zindex-modal-mask: 1000;
$zindex-message: 1010;
$zindex-notification: 1010;
$zindex-popover: 1030;
$zindex-dropdown: 1050;
$zindex-picker: 1050;
$zindex-tooltip: 1060;

// Screen sizes
$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1200px;
$screen-xxl: 1600px;

// Grid system
$grid-columns: 24;
$grid-gutter-width: 0;

// Editor specific variables
$editor-background: #ffffff;
$editor-toolbar-background: #fafafa;
$editor-toolbar-border: #f0f0f0;
$editor-sidebar-background: #f5f5f5;
$editor-sidebar-width: 280px;
$editor-content-padding: 24px;
$editor-line-height: 1.6;
$editor-font-size: 16px;

// Dark theme variables
$dark-bg: #141414;
$dark-bg-secondary: #1f1f1f;
$dark-bg-tertiary: #262626;
$dark-text: rgba(255, 255, 255, 0.85);
$dark-text-secondary: rgba(255, 255, 255, 0.65);
$dark-border: #434343;

// Custom application colors
$app-primary: #1890ff;
$app-secondary: #722ed1;
$app-accent: #13c2c2;
$app-background: #f0f2f5;
$app-surface: #ffffff;
$app-error: #ff4d4f;
$app-warning: #faad14;
$app-info: #1890ff;
$app-success: #52c41a;
